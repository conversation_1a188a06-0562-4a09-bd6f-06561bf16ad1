<template>
  <div id="app">
    <!-- 页面头部 -->
    <div class="header">
      <h1>专卖案件文书大模型智办系统</h1>
      <p>智能化案件处理 · 高效文书生成 · 专业案件分析</p>
    </div>

    <!-- 左侧边栏 -->
    <div class="left-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <!-- 问答历史记录 -->
      <div class="qa-history">
        <button class="sidebar-toggle" @click="toggleSidebar">
          <i>◀</i>
        </button>
        <div class="qa-history-content">
          <div class="qa-history-header">
            <h4>对话</h4>
            <el-button size="small" type="text" @click="clearHistory">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>

          <div class="qa-history-list" v-if="conversationHistory.length > 0">
            <div class="conversation-item"
                 v-for="(conversation, index) in conversationHistory.slice(0, 8)"
                 :key="conversation.id"
                 :class="{ active: conversation.id === currentConversationId }"
                 @click="openConversation(conversation)">
              <div class="conversation-title">{{ conversation.title }}</div>
            </div>
          </div>
          <div v-else class="no-history">
            <p>暂无对话历史</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧边栏 -->
    <div class="right-sidebar" :class="{ collapsed: rightSidebarCollapsed }">
      <button class="right-sidebar-toggle" @click="toggleRightSidebar">
        <i>▶</i>
      </button>
      <div class="recent-activity-content">
        <div class="recent-activity-header">
          <h4>
            <el-icon><Clock /></el-icon>
            最近活动
          </h4>
        </div>
        <div class="recent-activity-list">
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.time"
              :type="activity.type"
              size="small">
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 主内容区域 -->
      <div class="main-content" :class="{ 'right-sidebar-collapsed': rightSidebarCollapsed }">
        <!-- 主对话区域 -->
        <div class="main-chat-area" v-if="showMainChat">
          <div class="main-chat-header">
            <h3>智能助手对话</h3>
            <el-button size="small" @click="showMainChat = false">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="main-chat-content">
            <div class="main-chat-messages">
              <div class="main-chat-item" v-for="(item, index) in mainChatHistory" :key="index">
                <div class="main-chat-question">
                  <div class="main-chat-bubble user-bubble">
                    <div class="main-chat-text">{{ item.question }}</div>
                  </div>
                  <div class="main-chat-avatar user-avatar">
                    <el-icon><User /></el-icon>
                  </div>
                </div>
                <div class="main-chat-answer" v-if="item.answer">
                  <div class="main-chat-avatar bot-avatar">
                    <el-icon>🤖</el-icon>
                  </div>
                  <div class="main-chat-bubble bot-bubble">
                    <div class="main-chat-text" v-html="item.answer"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="main-features-grid" v-else>
          <!-- 系统功能 - 单独一行 -->
          <div class="system-functions-row">
            <div class="feature-section system-functions">
              <div class="regulation-header-row">
                <span class="title-span">🚀 系统功能</span>
              </div>
              <div class="function-cards-container">
                <!-- 案卷查询 -->
                <div class="function-card query-card" @click="navigateToFunction('query')">
                  <div class="function-icon query-icon">
                    <el-icon><Folder /></el-icon>
                  </div>
                  <div class="function-title">案卷查询</div>
                  <div class="function-description">
                    查询已生成的案卷档案，支持编辑和导出
                  </div>
                </div>
                <!-- 案卷生成 -->
                <div class="function-card generation-card" @click="navigateToFunction('generation')">
                  <div class="function-icon generation-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="function-title">案卷生成</div>
                  <div class="function-description">
                    智能生成案件文书，自动填充案件信息
                  </div>
                </div>
                <!-- 案卷评查 -->
                <div class="function-card review-card" @click="navigateToFunction('review')">
                  <div class="function-icon review-icon">
                    <el-icon><Check /></el-icon>
                  </div>
                  <div class="function-title">案卷评查</div>
                  <div class="function-description">
                    智能审核案件材料，检查文书规范性
                  </div>
                </div>
                <!-- 智能对话 -->
                <div class="function-card chat-card" @click="navigateToFunction('analysis')">
                  <div class="function-icon chat-icon">
                    <span style="font-size: 20px;">💬</span>
                  </div>
                  <div class="function-title">多维分析</div>
                  <div class="function-description">
                    与AI助手进行专业对话咨询
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统概览和法规更新公告 - 同一行 -->
          <div class="bottom-sections-row">
            <!-- 系统概览 -->
            <div class="feature-section stats-overview compact">
              <div class="regulation-header-row">
                <span class="title-span">📊 系统概览</span>
              </div>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number">{{ stats.totalCases }}</div>
                  <div class="stat-label">累计处理案件</div>
                  <el-progress :percentage="75" :show-text="false" :stroke-width="4" color="#409EFF"></el-progress>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.todayCases }}</div>
                  <div class="stat-label">今日新增案件</div>
                  <el-progress :percentage="60" :show-text="false" :stroke-width="4" color="#67C23A"></el-progress>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.pendingReview }}</div>
                  <div class="stat-label">待评查案件</div>
                  <el-progress :percentage="30" :show-text="false" :stroke-width="4" color="#E6A23C"></el-progress>
                </div>
              </div>
            </div>

            <!-- 法规更新公告 -->
            <div class="feature-section regulation-updates compact">
              <div class="regulation-header-row">
                <span class="title-span">📄 法规更新公告</span>
                <a href="#" class="view-more-link" @click="navigateToRegulations">
                  查看更多法规
                  <el-icon><ArrowRight /></el-icon>
                </a>
              </div>
              <div class="regulation-items">
                <div class="regulation-item" v-for="(regulation, index) in regulations.slice(0, 3)" :key="index">
                  <div class="regulation-header">
                    <el-tag :type="regulation.type" size="small">
                      <el-icon><Document /></el-icon>
                      {{ regulation.category }}
                    </el-tag>
                    <div class="regulation-title" :data-title="regulation.title">{{ regulation.title }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问答输入框区域 -->
    <div class="qa-section">
      <div class="qa-examples-section">
        <div class="qa-examples-container">
          <div class="qa-examples-list">
            <div class="qa-example-item">
              <div class="qa-example-question">
                <div class="qa-bubble">
                  <div class="qa-content">如何快速生成案卷？</div>
                </div>
                <div class="qa-icon user-icon">
                  <el-icon><User /></el-icon>
                </div>
              </div>
              <div class="qa-example-answer">
                <div class="qa-icon bot-icon">
                  <el-icon>🤖</el-icon>
                </div>
                <div class="qa-bubble">
                  <div class="qa-content">
                    <div class="thinking-process">正在思考您的问题<span class="thinking-dots"></span></div>
                    智能生成案卷可点击<router-link to="/case/generate" style="color: #409EFF; text-decoration: none; font-weight: 600;">此处</router-link>，系统支持单独生成特定文书和批量生成所有案卷文件两种模式。只需填写基本案件信息，AI将自动生成规范的案卷文书。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="qa-input-container">
        <div class="qa-input-wrapper">
          <el-input
            v-model="questionInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的问题，例如：如何处理无证经营案件？行政处罚的程序是什么？"
            class="qa-input"
            @keydown.ctrl.enter="sendQuestion"
            resize="none">
          </el-input>

          <!-- 已上传文件显示区域 -->
          <div class="uploaded-files" v-if="uploadedFiles.length > 0">
            <div class="file-item" v-for="(file, index) in uploadedFiles" :key="index">
              <el-icon class="file-icon">
                <Document v-if="file.type === 'file'"></Document>
                <Picture v-else></Picture>
              </el-icon>
              <span class="file-name">{{ file.name }}</span>
              <el-button size="small" type="text" @click="removeFile(index)" class="remove-btn">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="qa-actions">
            <div class="qa-buttons">
              <!-- 上传按钮 -->
              <el-upload
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                accept="image/*"
                @change="handleImageUpload">
                <el-button size="small">
                  <el-icon><PictureFilled /></el-icon>
                  图片
                </el-button>
              </el-upload>

              <el-upload
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                accept=".pdf,.doc,.docx,.txt,.xls,.xlsx"
                @change="handleFileUpload">
                <el-button size="small">
                  <el-icon><Paperclip /></el-icon>
                  附件
                </el-button>
              </el-upload>

              <!-- 操作按钮 -->
              <el-button @click="clearInput" size="small">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
              <el-button type="primary" @click="sendQuestion" size="small" :loading="isAnswering">
                <el-icon><Top /></el-icon>
                {{ isAnswering ? '思考中...' : '发送问题' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import {
  Delete,
  Clock,
  Close,
  User,
  Folder,
  Document,
  Check,
  ArrowRight,
  Picture,
  PictureFilled,
  Paperclip,
  Top
} from '@element-plus/icons-vue';

// 类型定义
interface UploadedFile {
  name: string;
  type: 'image' | 'file';
  file: File;
}

interface Activity {
  id: number;
  content: string;
  time: string;
  type: string;
}

interface Regulation {
  title: string;
  category: string;
  date: string;
  type: string;
}

interface Conversation {
  id: number;
  title: string;
  time: string;
  messages: ChatMessage[];
}

interface ChatMessage {
  question: string;
  answer: string;
  thinking?: boolean;
}

interface Stats {
  totalCases: number;
  todayCases: number;
  pendingReview: number;
  efficiency: number;
}

// 路由
const router = useRouter();

// 响应式数据
const sidebarCollapsed = ref(false);
const rightSidebarCollapsed = ref(false);
const uploadedFiles = ref<UploadedFile[]>([]);
const showMainChat = ref(false);
const mainChatHistory = ref<ChatMessage[]>([]);
const conversationHistory = ref<Conversation[]>([]);
const currentConversationId = ref<number | null>(null);

const stats = reactive<Stats>({
  totalCases: 1248,
  todayCases: 23,
  pendingReview: 15,
  efficiency: 85
});

// 法规更新公告数据
const regulations = ref<Regulation[]>([
  {
    title: '关于印发《烟草专卖行政处罚程序规定》的通知',
    category: '行政处罚',
    date: '2023-12-15',
    type: 'primary'
  },
  {
    title: '关于加强烟草专卖行政处罚案件办理工作的通知',
    category: '案件办理',
    date: '2023-11-20',
    type: 'success'
  },
  {
    title: '关于进一步规范烟草专卖行政处罚自由裁量权的指导意见',
    category: '行政处罚',
    date: '2023-10-08',
    type: 'warning'
  },
  {
    title: '关于印发《烟草专卖许可证管理办法实施细则》的通知',
    category: '许可管理',
    date: '2022-12-25',
    type: 'info'
  },
  {
    title: '关于进一步加强烟草专卖品市场监管工作的通知',
    category: '市场监管',
    date: '2022-11-15',
    type: 'success'
  },
  {
    title: '关于印发《烟草专卖行政处罚证据规定》的通知',
    category: '行政处罚',
    date: '2022-09-30',
    type: 'primary'
  },
  {
    title: '关于加强烟草专卖行政执法与刑事司法衔接工作的实施意见',
    category: '执法衔接',
    date: '2022-08-12',
    type: 'danger'
  },
  {
    title: '关于印发《烟草专卖行政处罚听证程序规定》的通知',
    category: '行政处罚',
    date: '2022-07-20',
    type: 'warning'
  },
  {
    title: '关于进一步规范烟草专卖行政执法自由裁量权的指导意见',
    category: '行政执法',
    date: '2022-06-15',
    type: 'info'
  },
  {
    title: '关于印发《烟草专卖行政处罚案卷评查办法》的通知',
    category: '案卷评查',
    date: '2022-05-10',
    type: 'success'
  }
]);

const recentActivities = ref<Activity[]>([
  {
    id: 1,
    content: '惠阳烟处（2025）第5号案件文书生成完成',
    time: '2025-01-04 15:30',
    type: 'success'
  },
  {
    id: 2,
    content: '徐某案件通过了智能评查',
    time: '2025-01-04 14:45',
    type: 'primary'
  },
  {
    id: 3,
    content: '完成1325条卷烟扣押清单生成',
    time: '2025-01-04 13:20',
    type: 'warning'
  },
  {
    id: 4,
    content: '惠州市惠阳区案件数据同步完成',
    time: '2025-01-04 10:00',
    type: 'info'
  }
]);

// 问答相关数据
const questionInput = ref('');
const isAnswering = ref(false);

// 初始化对话历史记录
conversationHistory.value = [
  {
    id: 1,
    title: '查询1-5月双喜相关案件，并将结果填充到文件中',
    time: '今天 14:30',
    messages: []
  },
  {
    id: 2,
    title: '帮我分析一下各区域的案件总数情况',
    time: '今天 13:45',
    messages: []
  },
  {
    id: 3,
    title: '今天是什么节日',
    time: '今天 10:20',
    messages: []
  },
  {
    id: 4,
    title: '你能做什么',
    time: '昨天 16:15',
    messages: []
  },
  {
    id: 5,
    title: '构建注册条文知识库开发结合...',
    time: '昨天 14:30',
    messages: []
  },
  {
    id: 6,
    title: '为何nacos-client-2.0.2无...',
    time: '昨天 11:20',
    messages: []
  },
  {
    id: 7,
    title: 'nacos-client-2.0.2无法连...',
    time: '2天前',
    messages: []
  }
];

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

const toggleRightSidebar = () => {
  rightSidebarCollapsed.value = !rightSidebarCollapsed.value;
};

const navigateToFunction = (type: string) => {
  const messages = {
    generation: '正在进入案卷生成模块...',
    review: '正在进入案卷评查模块...',
    analysis: '正在进入案件多维分析模块...',
    query: '正在进入案卷查询模块...'
  };

  ElMessage({
    message: messages[type as keyof typeof messages],
    type: 'success',
    duration: 2000
  });

  // 页面跳转逻辑
  setTimeout(() => {
    if (type === 'generation') {
      router.push('/case/generate');
    } else if (type === 'review') {
      router.push('/case/review');
    } else if (type === 'analysis') {
      router.push('/case/analysis');
    } else if (type === 'query') {
      router.push('/case/query');
    }
  }, 1000);
};

const navigateToRegulations = () => {
  ElMessage({
    message: '正在进入法规更新公告页面...',
    type: 'info',
    duration: 2000
  });

  setTimeout(() => {
    router.push('/regulations');
  }, 1000);
};

// 文件上传处理方法
const handleImageUpload = (file: any) => {
  const fileObj: UploadedFile = {
    name: file.name,
    type: 'image',
    file: file.raw
  };
  uploadedFiles.value.push(fileObj);
  ElMessage({
    message: `图片 "${file.name}" 上传成功`,
    type: 'success',
    duration: 2000
  });
};

const handleFileUpload = (file: any) => {
  const fileObj: UploadedFile = {
    name: file.name,
    type: 'file',
    file: file.raw
  };
  uploadedFiles.value.push(fileObj);
  ElMessage({
    message: `附件 "${file.name}" 上传成功`,
    type: 'success',
    duration: 2000
  });
};

const removeFile = (index: number) => {
  const fileName = uploadedFiles.value[index].name;
  uploadedFiles.value.splice(index, 1);
  ElMessage({
    message: `已删除文件 "${fileName}"`,
    type: 'info',
    duration: 2000
  });
};

// 发送问题
const sendQuestion = () => {
  if (!questionInput.value.trim()) {
    ElMessage({
      message: '请输入您的问题',
      type: 'warning',
      duration: 2000
    });
    return;
  }

  const question = questionInput.value.trim();
  isAnswering.value = true;

  // 显示主对话区域
  showMainChat.value = true;

  // 创建新的对话记录或添加到当前对话
  if (!currentConversationId.value) {
    // 创建新对话
    const newConversation: Conversation = {
      id: Date.now(),
      title: question.length > 30 ? question.substring(0, 30) + '...' : question,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      messages: []
    };
    conversationHistory.value.unshift(newConversation);
    currentConversationId.value = newConversation.id;
  }

  // 先添加用户问题到主对话历史记录
  const newMainChatItem: ChatMessage = {
    question: question,
    answer: '',
    thinking: true
  };
  mainChatHistory.value.push(newMainChatItem);

  // 添加到当前对话的消息记录
  const currentConversation = conversationHistory.value.find(c => c.id === currentConversationId.value);
  if (currentConversation) {
    currentConversation.messages.push(newMainChatItem);
  }

  // 显示思考过程
  setTimeout(() => {
    const mainIndex = mainChatHistory.value.length - 1;
    const thinkingText = '<div class="thinking-process">正在思考您的问题<span class="thinking-dots"></span></div>';
    mainChatHistory.value[mainIndex].answer = thinkingText;
  }, 500);

  // 模拟AI回答
  setTimeout(() => {
    const answers: Record<string, string> = {
      '如何处理无证经营案件？': '无证经营案件处理流程：1. 现场检查并收集证据；2. 制作现场检查笔录；3. 调取相关证据材料；4. 制作调查询问笔录；5. 案件审理；6. 制作行政处罚决定书；7. 送达处罚决定书；8. 执行处罚决定。',
      '如何快速生成案卷？': '智能生成案卷可点击案卷生成模块，系统支持单独生成特定文书和批量生成所有案卷文件两种模式。只需填写基本案件信息，AI将自动生成规范的案卷文书。',
      '行政处罚的程序是什么？': '行政处罚程序包括：1. 立案；2. 调查取证；3. 告知当事人权利；4. 听取当事人陈述申辩；5. 案件审理；6. 作出处罚决定；7. 送达处罚决定书；8. 执行。对于较大数额罚款，还需要进行听证程序。',
      '案件文书模板在哪里？': '案件文书模板可以在系统的"案卷生成"模块中找到，包括现场检查笔录、调查询问笔录、行政处罚决定书等标准模板。您也可以在文档窗口中查看最近使用的模板文件。',
      '如何计算罚款金额？': '罚款金额计算需要根据《烟草专卖法》及相关法规，结合违法行为的性质、情节、危害后果等因素，在法定幅度内进行自由裁量。系统提供自动计算功能，会根据案件具体情况给出建议金额。',
      '证据收集要注意什么？': '证据收集要注意：1. 证据的合法性、真实性、关联性；2. 现场拍照要清晰完整；3. 笔录制作要规范；4. 物证保全要妥善；5. 证人证言要客观；6. 书证要原件或核实无误的复印件；7. 证据链要完整。'
    };

    let answer = answers[question];
    if (!answer) {
      // 简单的关键词匹配
      if (question.includes('无证') || question.includes('经营')) {
        answer = '无证经营是指未取得烟草专卖零售许可证经营烟草制品零售业务的行为。处理时需要收集充分证据，包括现场检查笔录、涉案物品清单、当事人身份证明等。';
      } else if (question.includes('罚款') || question.includes('金额')) {
        answer = '罚款金额的确定需要综合考虑违法行为的性质、情节、社会危害程度等因素，在法定幅度内合理裁量。建议参考相关裁量基准和类似案例。';
      } else if (question.includes('文书') || question.includes('模板')) {
        answer = '系统提供完整的案件文书模板库，包括各类笔录、决定书等。您可以通过案卷生成功能选择合适的模板，系统会自动填充相关信息。';
      } else {
        answer = '感谢您的提问。这是一个很好的问题，建议您查阅相关法律法规或咨询专业人员。如果是系统操作问题，可以查看帮助文档或联系技术支持。';
      }
    }

    // 更新主对话区域的答案
    const mainIndex = mainChatHistory.value.length - 1;
    mainChatHistory.value[mainIndex].answer = answer;
    mainChatHistory.value[mainIndex].thinking = false;

    questionInput.value = '';
    isAnswering.value = false;

    ElMessage({
      message: '回答已生成',
      type: 'success',
      duration: 2000
    });
  }, 3000);
};

// 清空输入
const clearInput = () => {
  questionInput.value = '';
};

// 清空历史
const clearHistory = () => {
  conversationHistory.value = [];
  mainChatHistory.value = [];
  currentConversationId.value = null;
  ElMessage({
    message: '对话历史已清空',
    type: 'info',
    duration: 2000
  });
};

// 打开对话
const openConversation = (conversation: Conversation) => {
  currentConversationId.value = conversation.id;
  mainChatHistory.value = [...conversation.messages];
  showMainChat.value = true;
};

// 生命周期
onMounted(() => {
  // 模拟实时数据更新
  setInterval(() => {
    // 随机更新今日案件数
    if (Math.random() > 0.8) {
      stats.todayCases += Math.floor(Math.random() * 3);
    }
  }, 30000);
});
</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

#app {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面头部样式 */
.header {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
}

.header h1 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
  letter-spacing: 2px;
}

.header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 300;
}

/* 左侧边栏样式 */
.left-sidebar {
  position: absolute;
  left: 0;
  top: 140px;
  width: 320px;
  height: calc(100vh - 140px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.left-sidebar.collapsed {
  width: 60px;
}

.sidebar-toggle {
  position: absolute;
  right: -15px;
  top: 20px;
  width: 30px;
  height: 30px;
  background: #409EFF;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1001;
}

.sidebar-toggle:hover {
  background: #337ecc;
  transform: scale(1.1);
}

.collapsed .sidebar-toggle i {
  transform: rotate(180deg);
}

.qa-history {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.qa-history-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.collapsed .qa-history-content {
  display: none;
}

.qa-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.qa-history-header h4 {
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.qa-history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.conversation-item {
  padding: 12px 15px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.conversation-item:hover {
  background: rgba(64, 158, 255, 0.1);
  border-left-color: #409EFF;
  transform: translateX(5px);
}

.conversation-item.active {
  background: rgba(64, 158, 255, 0.15);
  border-left-color: #409EFF;
}

.conversation-title {
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-history {
  text-align: center;
  color: #999;
  font-style: italic;
  margin-top: 50px;
}

/* 右侧边栏样式 */
.right-sidebar {
  position: absolute;
  right: 0;
  top: 140px;
  width: 300px;
  height: calc(100vh - 140px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-left: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
}

.right-sidebar.collapsed {
  width: 60px;
}

.right-sidebar-toggle {
  position: absolute;
  left: -15px;
  top: 20px;
  width: 30px;
  height: 30px;
  background: #67C23A;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
  z-index: 1001;
}

.right-sidebar-toggle:hover {
  background: #529b2e;
  transform: scale(1.1);
}

.collapsed .right-sidebar-toggle i {
  transform: rotate(180deg);
}

.recent-activity-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.collapsed .recent-activity-content {
  display: none;
}

.recent-activity-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.recent-activity-header h4 {
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recent-activity-list {
  margin-top: 15px;
}

/* 主容器样式 */
.main-container {
  margin-left: 320px;
  margin-right: 300px;
  padding: 20px;
  min-height: calc(100vh - 140px);
  transition: all 0.3s ease;
}

.left-sidebar.collapsed ~ .main-container {
  margin-left: 60px;
}

.right-sidebar.collapsed ~ .main-container {
  margin-right: 60px;
}

.main-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 600px;
  transition: all 0.3s ease;
}

/* 主对话区域样式 */
.main-chat-area {
  height: 500px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.main-chat-header {
  background: linear-gradient(135deg, #409EFF, #337ecc);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-chat-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.main-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.main-chat-messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-chat-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-chat-question,
.main-chat-answer {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.main-chat-question {
  flex-direction: row-reverse;
}

.main-chat-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-avatar {
  background: #409EFF;
  color: white;
}

.bot-avatar {
  background: #67C23A;
  color: white;
}

.main-chat-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.user-bubble {
  background: #409EFF;
  color: white;
  border-bottom-right-radius: 6px;
}

.bot-bubble {
  background: #f5f5f5;
  color: #333;
  border-bottom-left-radius: 6px;
}

.main-chat-text {
  line-height: 1.5;
  font-size: 0.95rem;
}

/* 功能网格样式 */
.main-features-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.system-functions-row,
.bottom-sections-row {
  display: flex;
  gap: 30px;
}

.bottom-sections-row {
  align-items: stretch;
}

.feature-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.feature-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.system-functions {
  flex: 1;
}

.stats-overview.compact,
.regulation-updates.compact {
  flex: 1;
}

.regulation-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.title-span {
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-more-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.view-more-link:hover {
  color: #337ecc;
  transform: translateX(3px);
}

/* 功能卡片样式 */
.function-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.function-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.function-card:hover::before {
  left: 100%;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.query-card:hover {
  border-color: #409EFF;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.generation-card:hover {
  border-color: #67C23A;
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}

.review-card:hover {
  border-color: #E6A23C;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}

.chat-card:hover {
  border-color: #F56C6C;
  background: linear-gradient(135deg, #fce4ec, #f8bbd9);
}

.function-icon {
  width: 50px;
  height: 50px;
  margin: 0 auto 15px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s ease;
}

.query-icon {
  background: linear-gradient(135deg, #409EFF, #337ecc);
  color: white;
}

.generation-icon {
  background: linear-gradient(135deg, #67C23A, #529b2e);
  color: white;
}

.review-icon {
  background: linear-gradient(135deg, #E6A23C, #b88230);
  color: white;
}

.chat-icon {
  background: linear-gradient(135deg, #F56C6C, #c45656);
  color: white;
}

.function-card:hover .function-icon {
  transform: scale(1.1) rotate(5deg);
}

.function-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.function-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* 统计概览样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.8);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

/* 法规更新样式 */
.regulation-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.regulation-item {
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.regulation-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-left-color: #409EFF;
  transform: translateX(5px);
}

.regulation-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.regulation-title {
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* 问答区域样式 */
.qa-section {
  position: fixed;
  bottom: 0;
  left: 320px;
  right: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding: 20px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 100;
}

/* 左侧边栏折叠时调整问答区域左边距 */
.left-sidebar.collapsed ~ .qa-section {
  left: 60px;
}

/* 右侧边栏折叠时调整问答区域右边距 */
.right-sidebar.collapsed ~ .qa-section {
  right: 60px;
}

.qa-examples-section {
  margin-bottom: 15px;
}

.qa-examples-container {
  max-height: 200px;
  overflow-y: auto;
}

.qa-examples-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.qa-example-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.qa-example-question,
.qa-example-answer {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.qa-example-question {
  flex-direction: row-reverse;
}

.qa-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
}

.user-icon {
  background: #409EFF;
  color: white;
}

.bot-icon {
  background: #67C23A;
  color: white;
}

.qa-bubble {
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 16px;
  word-wrap: break-word;
}

.qa-example-question .qa-bubble {
  background: #409EFF;
  color: white;
  border-bottom-right-radius: 4px;
}

.qa-example-answer .qa-bubble {
  background: #f5f5f5;
  color: #333;
  border-bottom-left-radius: 4px;
}

.qa-content {
  line-height: 1.4;
  font-size: 0.9rem;
}

.thinking-process {
  color: #999;
  font-style: italic;
  margin-bottom: 8px;
}

.thinking-dots::after {
  content: '...';
  animation: thinking 1.5s infinite;
}

@keyframes thinking {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.qa-input-container {
  background: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.qa-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.qa-input {
  border-radius: 8px;
}

.qa-input :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  padding: 12px;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}

.qa-input :deep(.el-textarea__inner):focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.uploaded-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f5f5f5;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #666;
}

.file-icon {
  color: #409EFF;
}

.file-name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  padding: 2px;
  color: #999;
}

.remove-btn:hover {
  color: #f56c6c;
}

.qa-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qa-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.upload-demo {
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-container {
    margin-left: 60px;
    margin-right: 60px;
  }

  .left-sidebar,
  .right-sidebar {
    width: 60px;
  }

  .qa-section {
    left: 60px;
    right: 60px;
  }

  .qa-history-content,
  .recent-activity-content {
    display: none;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }

  .header p {
    font-size: 1rem;
  }

  .main-container {
    margin-left: 0;
    margin-right: 0;
    padding: 10px;
  }

  .left-sidebar,
  .right-sidebar {
    display: none;
  }

  .qa-section {
    left: 0;
    right: 0;
  }

  .function-cards-container {
    grid-template-columns: 1fr;
  }

  .bottom-sections-row {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-section {
  animation: fadeIn 0.6s ease-out;
}

.function-card {
  animation: fadeIn 0.6s ease-out;
}

.conversation-item {
  animation: fadeIn 0.4s ease-out;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF, #337ecc);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #337ecc, #2b6cb0);
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.el-progress-bar__outer) {
  border-radius: 2px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 0.8rem;
  color: #999;
}
</style>