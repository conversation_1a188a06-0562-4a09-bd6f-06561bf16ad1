<template>
  <div>
    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 查询条件区域 -->
      <div class="query-container">
        <div class="query-title">
          <el-icon><Search /></el-icon>
          查询条件
        </div>

        <div class="query-form">
          <el-form-item label="案件编号">
            <el-input
              v-model="queryForm.caseNumber"
              placeholder="请输入案件编号"
              clearable>
            </el-input>
          </el-form-item>

          <el-form-item label="当事人姓名">
            <el-input
              v-model="queryForm.partyName"
              placeholder="请输入当事人姓名"
              clearable>
            </el-input>
          </el-form-item>

          <el-form-item label="案件状态">
            <el-select
              v-model="queryForm.status"
              placeholder="请选择案件状态"
              clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="未归档" value="pending"></el-option>
              <el-option label="已归档" value="archived"></el-option>
              <el-option label="已结案" value="closed"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="案件类型">
            <el-select
              v-model="queryForm.caseType"
              placeholder="请选择案件类型"
              clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="无证经营" value="unlicensed"></el-option>
              <el-option label="超范围经营" value="beyond_scope"></el-option>
              <el-option label="销售假烟" value="fake_cigarettes"></el-option>
              <el-option label="其他违法" value="other"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="办案人员">
            <el-input
              v-model="queryForm.officer"
              placeholder="请输入办案人员姓名"
              clearable>
            </el-input>
          </el-form-item>
        </div>

        <div class="query-actions">
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="searchCases">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </div>
      </div>

      <!-- 查询结果区域 -->
      <div class="results-container">
        <div class="results-header">
          <div class="results-title">
            <el-icon><List /></el-icon>
            查询结果 (共 {{ total }} 条)
          </div>

          <div class="batch-actions">
            <el-button
              type="success"
              :disabled="selectedCases.length === 0"
              @click="batchExport">
              <el-icon><Download /></el-icon>
              批量导出 ({{ selectedCases.length }})
            </el-button>
            <el-button
              type="warning"
              :disabled="selectedPendingCases.length === 0"
              @click="batchEdit">
              <el-icon><Edit /></el-icon>
              批量编辑 ({{ selectedPendingCases.length }})
            </el-button>
          </div>
        </div>

        <el-table
          :data="caseList"
          class="case-table"
          @selection-change="handleSelectionChange"
          v-loading="loading">

          <el-table-column type="selection" width="55"></el-table-column>

          <el-table-column prop="caseNumber" label="案件编号" width="260">
            <template #default="scope">
              <el-link type="primary" @click="viewCase(scope.row)">
                {{ scope.row.caseNumber }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="partyName" label="当事人" width="100"></el-table-column>

          <el-table-column prop="caseType" label="案件类型" width="110">
            <template #default="scope">
              {{ getCaseTypeText(scope.row.caseType) }}
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="90">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                class="status-tag">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="officer" label="办案人员" width="100"></el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="110"></el-table-column>

          <el-table-column prop="updateTime" label="更新时间" width="110"></el-table-column>

          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button size="small" @click="viewExistingFile(scope.row)">
                  查看文书
                </el-button>
                <el-tooltip
                  :content="'仅支持对未归档的案件进行案卷编辑'"
                  placement="top">
                  <el-button
                    size="small"
                    type="warning"
                    :disabled="scope.row.status === 'archived'"
                    @click="generateCase(scope.row)">
                    案卷生成
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  :content="'仅支持对已结案的案件进行案卷评查'"
                  placement="top">
                  <el-button
                    size="small"
                    type="warning"
                    :disabled="scope.row.status !== 'closed'"
                    @click="reviewCase(scope.row)">
                    案卷评查
                  </el-button>
                </el-tooltip>

                <el-button
                  size="small"
                  type="primary"
                  :disabled="scope.row.status === 'archived'"
                  @click="uploadScanFiles(scope.row)">
                  上传扫描件
                </el-button>

                <el-button
                  size="small"
                  type="success"
                  @click="exportCase(scope.row)">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 编辑案件对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑案件信息"
      width="800px"
      class="edit-dialog">

      <el-form :model="editForm" label-width="120px">
        <div class="edit-form">
          <el-form-item label="案件编号">
            <el-input v-model="editForm.caseNumber" disabled></el-input>
          </el-form-item>

          <el-form-item label="当事人姓名">
            <el-input v-model="editForm.partyName"></el-input>
          </el-form-item>

          <el-form-item label="案件类型">
            <el-select v-model="editForm.caseType">
              <el-option label="无证经营" value="unlicensed"></el-option>
              <el-option label="超范围经营" value="beyond_scope"></el-option>
              <el-option label="销售假烟" value="fake_cigarettes"></el-option>
              <el-option label="其他违法" value="other"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="办案人员">
            <el-input v-model="editForm.officer"></el-input>
          </el-form-item>

          <el-form-item label="案件描述" class="full-width">
            <el-input
              v-model="editForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入案件描述">
            </el-input>
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 扫描件上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传扫描件"
      width="600px"
      class="upload-dialog">

      <div class="upload-case-info">
        <p><strong>案件编号：</strong>{{ currentCase.caseNumber }}</p>
        <p><strong>当事人：</strong>{{ currentCase.partyName }}</p>
      </div>

      <div class="upload-area"
           @click="triggerFileInput"
           @dragover.prevent="handleDragOver"
           @dragleave.prevent="handleDragLeave"
           @drop.prevent="handleDrop"
           :class="{ dragover: isDragOver }">
        <div class="upload-icon">
          <el-icon><UploadFilled /></el-icon>
        </div>
        <div class="upload-text">点击或拖拽文件到此区域上传</div>
        <div class="upload-hint">支持 PDF、JPG、PNG、JPEG 格式，单个文件不超过 10MB</div>
      </div>

      <input
        type="file"
        multiple
        accept=".pdf,.jpg,.jpeg,.png"
        style="display: none"
        @change="handleFileSelect">

      <div class="file-list" v-if="uploadFiles.length > 0">
        <h4>已选择文件：</h4>
        <div class="file-item" v-for="(file, index) in uploadFiles" :key="index">
          <div class="file-info">
            <div class="file-icon">
              <el-icon v-if="file.type.includes('pdf')"><Document /></el-icon>
              <el-icon v-else><Picture /></el-icon>
            </div>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
          </div>
          <div class="file-actions">
            <el-button size="small" type="danger" @click="removeFile(index)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <div class="upload-progress" v-if="isUploading">
        <el-progress
          :percentage="uploadProgress"
          :stroke-width="6"
          color="#409EFF">
        </el-progress>
        <p style="text-align: center; margin-top: 8px; color: #606266;">{{ uploadProgressText }}</p>
      </div>

      <!-- 识别结果区域 -->
      <div class="recognition-results" v-if="recognitionResults.length > 0">
        <div class="recognition-title">
          <div class="recognition-title-left">
            <el-icon class="recognition-icon" v-if="isRecognizing"><Loading /></el-icon>
            <el-icon v-else><DocumentChecked /></el-icon>
            {{ isRecognizing ? '正在识别扫描件内容...' : '识别到以下文件' }}
          </div>
          <div class="recognition-title-right" v-if="!isRecognizing">
            <el-button type="text" size="small" @click="selectAllFiles">
              {{ allFilesSelected ? '取消全选' : '全选' }}
            </el-button>
          </div>
        </div>

        <ul class="recognition-list">
          <li class="recognition-item"
              v-for="(item, index) in recognitionResults"
              :key="index"
              :class="{ duplicate: item.isDuplicate }">
            <div class="recognition-item-checkbox">
              <el-checkbox v-model="item.selected" @change="onItemSelectionChange"></el-checkbox>
            </div>
            <div class="recognition-item-info">
              <div class="recognition-item-icon">
                <el-icon v-if="item.type === 'document'"><Document /></el-icon>
                <el-icon v-else-if="item.type === 'form'"><Tickets /></el-icon>
                <el-icon v-else-if="item.type === 'certificate'"><Medal /></el-icon>
                <el-icon v-else><Files /></el-icon>
              </div>
              <div class="recognition-item-details">
                <div class="recognition-item-name">{{ item.name }}</div>
                <div class="recognition-item-type">{{ item.typeText }}</div>
              </div>
            </div>
            <div class="recognition-item-actions">
              <el-tag v-if="item.isDuplicate" type="warning" size="small">已存在</el-tag>
              <el-button v-if="item.isDuplicate"
                        type="text"
                        size="small"
                        @click="viewExistingFile(item)">
                查看文书
              </el-button>
            </div>
          </li>
        </ul>

        <!-- 重复文件警告 -->
        <div class="duplicate-warning" v-if="hasDuplicateFiles">
          <el-icon class="duplicate-warning-icon"><Warning /></el-icon>
          <div class="duplicate-warning-text">
            检测到 {{ duplicateCount }} 个文件已存在，上传将覆盖原有文件
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button
            type="primary"
            @click="startUpload"
            :disabled="uploadFiles.length === 0 || isUploading || isRecognizing || recognitionResults.filter(item => item.selected).length === 0">
            {{ getUploadButtonText() }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出进度对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">

      <div class="export-progress">
        <div class="progress-icon">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="progress-text">{{ exportProgressText }}</div>
        <el-progress
          :percentage="exportProgress"
          :stroke-width="8"
          color="#409EFF">
        </el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import {
  Search,
  Refresh,
  List,
  Download,
  Edit,
  UploadFilled,
  Document,
  Picture,
  Delete,
  Loading,
  DocumentChecked,
  Warning,
  Tickets,
  Medal,
  Files,
  View,
  CircleCheck,
  WarningFilled
} from '@element-plus/icons-vue';

// 类型定义
interface QueryForm {
  caseNumber: string;
  partyName: string;
  status: string;
  caseType: string;
  dateRange: string[];
  officer: string;
}

interface CaseItem {
  id: number;
  caseNumber: string;
  partyName: string;
  caseType: string;
  status: string;
  officer: string;
  createTime: string;
  updateTime: string;
  description: string;
}

interface EditForm {
  id: string;
  caseNumber: string;
  partyName: string;
  caseType: string;
  officer: string;
  description: string;
}

interface RecognitionResult {
  name: string;
  type: string;
  typeText: string;
  isDuplicate: boolean;
  selected: boolean;
}

// 路由
const router = useRouter();

// 响应式数据
const queryForm = reactive<QueryForm>({
  caseNumber: '',
  partyName: '',
  status: '',
  caseType: '',
  dateRange: [],
  officer: ''
});

const caseList = ref<CaseItem[]>([]);
const allCases = ref<CaseItem[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const loading = ref(false);
const selectedCases = ref<CaseItem[]>([]);

// 编辑对话框
const editDialogVisible = ref(false);
const editForm = reactive<EditForm>({
  id: '',
  caseNumber: '',
  partyName: '',
  caseType: '',
  officer: '',
  description: ''
});

// 导出进度
const exportDialogVisible = ref(false);
const exportProgress = ref(0);
const exportProgressText = ref('正在准备导出...');

// 上传相关
const uploadDialogVisible = ref(false);
const currentCase = ref<CaseItem>({} as CaseItem);
const uploadFiles = ref<File[]>([]);
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const uploadProgressText = ref('');

// 识别相关
const isRecognizing = ref(false);
const recognitionResults = ref<RecognitionResult[]>([]);
const existingFiles = ref<string[]>([]);

// 计算属性
const selectedPendingCases = computed(() => {
  return selectedCases.value.filter(item => item.status === 'pending');
});

const hasDuplicateFiles = computed(() => {
  return recognitionResults.value.some(item => item.isDuplicate);
});

const duplicateCount = computed(() => {
  return recognitionResults.value.filter(item => item.isDuplicate).length;
});

const allFilesSelected = computed(() => {
  return recognitionResults.value.length > 0 && recognitionResults.value.every(item => item.selected);
});

// 方法
const loadMockData = () => {
  const mockCases: CaseItem[] = [];
  const caseTypes = ['unlicensed', 'beyond_scope', 'fake_cigarettes', 'other'];
  const statuses = ['pending', 'archived', 'closed'];
  const officers = ['张三', '李四', '王五', '赵六', '钱七'];
  const partyNames = ['陈某', '刘某', '黄某', '林某', '郑某', '吴某', '周某', '徐某'];

  for (let i = 1; i <= 50; i++) {
    const createDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
    const updateDate = new Date(createDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);

    mockCases.push({
      id: i,
      caseNumber: `惠阳烟处（2024）第${String(i).padStart(3, '0')}号`,
      partyName: partyNames[Math.floor(Math.random() * partyNames.length)],
      caseType: caseTypes[Math.floor(Math.random() * caseTypes.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      officer: officers[Math.floor(Math.random() * officers.length)],
      createTime: createDate.toISOString().split('T')[0],
      updateTime: updateDate.toISOString().split('T')[0],
      description: `案件${i}的详细描述信息...`
    });
  }

  allCases.value = mockCases;
  // allCases.value = []
};

const searchCases = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    let filteredCases = [...allCases.value];

    // 应用筛选条件
    if (queryForm.caseNumber) {
      filteredCases = filteredCases.filter(item =>
        item.caseNumber.includes(queryForm.caseNumber)
      );
    }

    if (queryForm.partyName) {
      filteredCases = filteredCases.filter(item =>
        item.partyName.includes(queryForm.partyName)
      );
    }

    if (queryForm.status) {
      filteredCases = filteredCases.filter(item =>
        item.status === queryForm.status
      );
    }

    if (queryForm.caseType) {
      filteredCases = filteredCases.filter(item =>
        item.caseType === queryForm.caseType
      );
    }

    if (queryForm.officer) {
      filteredCases = filteredCases.filter(item =>
        item.officer.includes(queryForm.officer)
      );
    }

    if (queryForm.dateRange && queryForm.dateRange.length === 2) {
      const [startDate, endDate] = queryForm.dateRange;
      filteredCases = filteredCases.filter(item =>
        item.createTime >= startDate && item.createTime <= endDate
      );
    }

    total.value = filteredCases.length;

    // 分页
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    caseList.value = filteredCases.slice(start, end);

    loading.value = false;
  }, 500);
};

const resetQuery = () => {
  Object.assign(queryForm, {
    caseNumber: '',
    partyName: '',
    status: '',
    caseType: '',
    dateRange: [],
    officer: ''
  });
  currentPage.value = 1;
  searchCases();
};

const handleSelectionChange = (selection: CaseItem[]) => {
  selectedCases.value = selection;
};

const viewCase = (row: CaseItem) => {
  ElMessage({
    message: `正在查看案件：${row.caseNumber}`,
    type: 'info'
  });
  // 这里可以跳转到案件详情页面
};

const editCase = (row: CaseItem) => {
  Object.assign(editForm, {
    id: row.id.toString(),
    caseNumber: row.caseNumber,
    partyName: row.partyName,
    caseType: row.caseType,
    officer: row.officer,
    description: row.description
  });
  editDialogVisible.value = true;
};

const generateCase = (row: CaseItem) => {
  // 将案件信息存储到localStorage中，供目标页面使用
  localStorage.setItem('selectedCase', JSON.stringify(row));
  // 跳转到案卷生成页面
  router.push('/case/generate');
};

const reviewCase = (row: CaseItem) => {
  // 将案件信息存储到localStorage中，供目标页面使用
  localStorage.setItem('selectedCase', JSON.stringify(row));
  // 跳转到案卷评查页面
  ElMessage({
    message: `正在跳转到案卷评查页面：${row.caseNumber}`,
    type: 'info'
  });
};

const saveEdit = () => {
  // 模拟保存
  const index = caseList.value.findIndex(item => item.id === parseInt(editForm.id));
  if (index !== -1) {
    Object.assign(caseList.value[index], editForm);
    caseList.value[index].updateTime = new Date().toISOString().split('T')[0];
  }

  ElMessage({
    message: '案件信息保存成功',
    type: 'success'
  });

  editDialogVisible.value = false;
};

const exportCase = (row: CaseItem) => {
  startExport([row]);
};

const batchExport = () => {
  if (selectedCases.value.length === 0) {
    ElMessage({
      message: '请先选择要导出的案件',
      type: 'warning'
    });
    return;
  }

  startExport(selectedCases.value);
};

const batchEdit = () => {
  if (selectedPendingCases.value.length === 0) {
    ElMessage({
      message: '请先选择未归档的案件进行编辑',
      type: 'warning'
    });
    return;
  }

  ElMessage({
    message: `正在批量编辑 ${selectedPendingCases.value.length} 个案件`,
    type: 'info'
  });
};

const startExport = (cases: CaseItem[]) => {
  exportDialogVisible.value = true;
  exportProgress.value = 0;
  exportProgressText.value = '正在准备导出...';

  // 模拟导出进度
  const interval = setInterval(() => {
    exportProgress.value += Math.random() * 20;

    if (exportProgress.value < 30) {
      exportProgressText.value = '正在生成Word文档...';
    } else if (exportProgress.value < 60) {
      exportProgressText.value = '正在处理文档格式...';
    } else if (exportProgress.value < 90) {
      exportProgressText.value = '正在压缩文件...';
    } else {
      exportProgressText.value = '即将完成...';
    }

    if (exportProgress.value >= 100) {
      exportProgress.value = 100;
      exportProgressText.value = '导出完成！';
      clearInterval(interval);

      setTimeout(() => {
        exportDialogVisible.value = false;
        ElMessage({
          message: `成功导出 ${cases.length} 个案件的Word文档`,
          type: 'success'
        });
      }, 1000);
    }
  }, 200);
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  searchCases();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  searchCases();
};

const getCaseTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'unlicensed': '无证经营',
    'beyond_scope': '超范围经营',
    'fake_cigarettes': '销售假烟',
    'other': '其他违法'
  };
  return typeMap[type] || type;
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '未归档',
    'archived': '已归档',
    'closed': '已结案'
  };
  return statusMap[status] || status;
};

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'archived': 'success',
    'closed': 'info'
  };
  return typeMap[status] || 'info';
};

const uploadScanFiles = (row: CaseItem) => {
  currentCase.value = row;
  uploadFiles.value = [];
  recognitionResults.value = [];
  isRecognizing.value = false;

  // 模拟该案件已存在的文件
  existingFiles.value = [
    '现场检查笔录',
    '调查询问笔录',
    '身份证复印件',
    '营业执照复印件'
  ];

  uploadDialogVisible.value = true;
};

const viewExistingFile = (row: CaseItem) => {
  ElMessage({
    message: `正在查看案件 ${row.caseNumber} 的文书`,
    type: 'info'
  });
};

const triggerFileInput = () => {
  // 这里需要通过ref访问文件输入框
  const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
  if (fileInput) {
    fileInput.click();
  }
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  addFiles(files);
  // 清空input值，允许重复选择同一文件
  target.value = '';
};

const handleDragOver = (event: DragEvent) => {
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  isDragOver.value = false;
};

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false;
  const files = Array.from(event.dataTransfer?.files || []);
  addFiles(files);
};

const addFiles = (files: File[]) => {
  const validFiles = files.filter(file => {
    // 检查文件类型
    const validTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      ElMessage({
        message: `文件 "${file.name}" 格式不支持，请选择 PDF、JPG、PNG 格式的文件`,
        type: 'warning'
      });
      return false;
    }

    // 检查文件大小（10MB）
    if (file.size > 10 * 1024 * 1024) {
      ElMessage({
        message: `文件 "${file.name}" 大小超过 10MB，请选择较小的文件`,
        type: 'warning'
      });
      return false;
    }

    return true;
  });

  uploadFiles.value.push(...validFiles);

  if (validFiles.length > 0) {
    ElMessage({
      message: `成功添加 ${validFiles.length} 个文件`,
      type: 'success'
    });

    // 开始识别文件内容
    startRecognition();
  }
};

const removeFile = (index: number) => {
  uploadFiles.value.splice(index, 1);
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const startRecognition = () => {
  isRecognizing.value = true;
  recognitionResults.value = [];

  // 模拟识别过程
  setTimeout(() => {
    const mockResults: RecognitionResult[] = [
      {
        name: '现场检查笔录',
        type: 'document',
        typeText: '笔录类',
        isDuplicate: true,
        selected: false
      },
      {
        name: '询问笔录',
        type: 'document',
        typeText: '笔录类',
        isDuplicate: false,
        selected: true
      },
      {
        name: '扣押清单',
        type: 'form',
        typeText: '清单类',
        isDuplicate: false,
        selected: true
      }
    ];

    recognitionResults.value = mockResults;
    isRecognizing.value = false;

    ElMessage({
      message: `识别完成，共识别出 ${mockResults.length} 个文件`,
      type: 'success'
    });
  }, 2000);
};

const startUpload = async () => {
  if (uploadFiles.value.length === 0) {
    ElMessage({
      message: '请先选择要上传的文件',
      type: 'warning'
    });
    return;
  }

  const selectedItems = recognitionResults.value.filter(item => item.selected);
  if (selectedItems.length === 0) {
    ElMessage({
      message: '请至少选择一个文件进行上传',
      type: 'warning'
    });
    return;
  }

  isUploading.value = true;
  uploadProgress.value = 0;
  uploadProgressText.value = '正在上传文件...';

  // 模拟上传进度
  const interval = setInterval(() => {
    uploadProgress.value += Math.random() * 15;

    if (uploadProgress.value >= 100) {
      uploadProgress.value = 100;
      uploadProgressText.value = '上传完成！';
      clearInterval(interval);

      setTimeout(() => {
        isUploading.value = false;
        uploadDialogVisible.value = false;
        ElMessage({
          message: `成功上传 ${selectedItems.length} 个文件`,
          type: 'success'
        });
      }, 1000);
    }
  }, 300);
};

const cancelUpload = () => {
  uploadDialogVisible.value = false;
  uploadFiles.value = [];
  recognitionResults.value = [];
  isUploading.value = false;
  isRecognizing.value = false;
};

const selectAllFiles = () => {
  const allSelected = allFilesSelected.value;
  recognitionResults.value.forEach(item => {
    item.selected = !allSelected;
  });
};

const onItemSelectionChange = () => {
  // 当单个项目选择状态改变时触发
};

const getUploadButtonText = () => {
  if (isUploading.value) {
    return '上传中...';
  }
  if (isRecognizing.value) {
    return '识别中...';
  }
  const selectedCount = recognitionResults.value.filter(item => item.selected).length;
  return selectedCount > 0 ? `上传选中的 ${selectedCount} 个文件` : '上传文件';
};

// 生命周期
onMounted(() => {
  loadMockData();
  searchCases();
});
</script>

<style scoped>
.main-container {
  max-width: 1400px;
  margin: 20px auto;
  padding: 0 20px;
}

.query-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.query-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.query-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.query-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.results-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.results-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

.case-table {
  height: calc(100vh - 610px);
  width: 100%;
  min-width: 1200px;
}

/* 表格滚动条样式 */
.results-container::-webkit-scrollbar {
  height: 8px;
}

.results-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.results-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.results-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 修复表格右侧固定列 */
:deep(.el-table__fixed-right) {
  right: 0 !important;
  z-index: 3;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-table__fixed-right-patch) {
  display: none !important;
}

.status-tag {
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 编辑对话框样式 */
:deep(.edit-dialog .el-dialog__body) {
  padding: 20px;
}

.edit-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.edit-form .full-width {
  grid-column: 1 / -1;
}

/* 导出进度对话框 */
.export-progress {
  text-align: center;
  padding: 20px;
}

.progress-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

/* 上传对话框样式 */
:deep(.upload-dialog .el-dialog__body) {
  padding: 20px;
}

/* 对话框动画 */
:deep(.el-dialog) {
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #409EFF;
  background: #f0f9ff;
}

.upload-area.dragover {
  border-color: #409EFF;
  background: #e6f7ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #909399;
}

.file-list {
  margin-top: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: #409EFF;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-progress {
  margin-top: 12px;
}

.upload-case-info {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
}

.upload-case-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

/* 识别结果样式 */
.recognition-results {
  margin-top: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #d1ecf1;
}

.recognition-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.recognition-title-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-title-right {
  display: flex;
  align-items: center;
}

.recognition-icon {
  color: #409EFF;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.recognition-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recognition-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  gap: 12px;
}

.recognition-item-checkbox {
  flex-shrink: 0;
}

.recognition-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.recognition-item.duplicate {
  border-color: #f56c6c;
  background: #fef0f0;
}

.recognition-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.recognition-item-icon {
  font-size: 20px;
  color: #409EFF;
}

.recognition-item-details {
  display: flex;
  flex-direction: column;
}

.recognition-item-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.recognition-item-type {
  font-size: 12px;
  color: #909399;
}

.recognition-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.duplicate-warning {
  background: #fff2e8;
  border: 1px solid #f7ba2a;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.duplicate-warning-icon {
  color: #f7ba2a;
  font-size: 18px;
}

.duplicate-warning-text {
  font-size: 14px;
  color: #e6a23c;
}

</style>