<template>
  <div class="case-query-container">
    <!-- 页面头部 -->
    <div class="header">
      <h1>案卷查询管理</h1>
      <p>查询案卷档案 · 编辑未归档案卷 · 批量导出文档</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <router-link to="/" class="breadcrumb-item">
          <el-icon class="breadcrumb-icon"><House /></el-icon>
          <span>首页</span>
        </router-link>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-item current">
          <el-icon class="breadcrumb-icon"><Folder /></el-icon>
          <span>案卷查询</span>
        </span>
      </div>

      <!-- 查询条件区域 -->
      <div class="query-container">
        <div class="query-title">
          <el-icon><Search /></el-icon>
          查询条件
        </div>

        <el-form :model="queryForm" class="query-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="案件编号">
                <el-input
                  v-model="queryForm.caseNumber"
                  placeholder="请输入案件编号"
                  clearable>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="当事人姓名">
                <el-input
                  v-model="queryForm.partyName"
                  placeholder="请输入当事人姓名"
                  clearable>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="案件状态">
                <el-select
                  v-model="queryForm.status"
                  placeholder="请选择案件状态"
                  clearable>
                  <el-option label="全部" value=""></el-option>
                  <el-option label="未归档" value="pending"></el-option>
                  <el-option label="已归档" value="archived"></el-option>
                  <el-option label="已结案" value="closed"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="案件类型">
                <el-select
                  v-model="queryForm.caseType"
                  placeholder="请选择案件类型"
                  clearable>
                  <el-option label="全部" value=""></el-option>
                  <el-option label="无证经营" value="unlicensed"></el-option>
                  <el-option label="超范围经营" value="beyond_scope"></el-option>
                  <el-option label="销售假烟" value="fake_cigarettes"></el-option>
                  <el-option label="其他违法" value="other"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="queryForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD">
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="办案人员">
                <el-input
                  v-model="queryForm.officer"
                  placeholder="请输入办案人员姓名"
                  clearable>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item>
                <div class="query-actions">
                  <el-button @click="resetQuery">
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                  <el-button type="primary" @click="searchCases">
                    <el-icon><Search /></el-icon>
                    查询
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 查询结果区域 -->
      <div class="results-container">
        <div class="results-header">
          <div class="results-title">
            <el-icon><List /></el-icon>
            查询结果 (共 {{ total }} 条)
          </div>

          <div class="batch-actions">
            <el-button
              type="success"
              :disabled="selectedCases.length === 0"
              @click="batchExport">
              <el-icon><Download /></el-icon>
              批量导出 ({{ selectedCases.length }})
            </el-button>
            <el-button
              type="warning"
              :disabled="selectedPendingCases.length === 0"
              @click="batchEdit">
              <el-icon><Edit /></el-icon>
              批量编辑 ({{ selectedPendingCases.length }})
            </el-button>
          </div>
        </div>

        <el-table
          :data="caseList"
          class="case-table"
          @selection-change="handleSelectionChange"
          v-loading="loading">

          <el-table-column type="selection" width="55"></el-table-column>

          <el-table-column prop="caseNumber" label="案件编号" width="180">
            <template #default="scope">
              <el-link type="primary" @click="viewCase(scope.row)">
                {{ scope.row.caseNumber }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="partyName" label="当事人" width="120"></el-table-column>

          <el-table-column prop="caseType" label="案件类型" width="120">
            <template #default="scope">
              {{ getCaseTypeText(scope.row.caseType) }}
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                class="status-tag">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="officer" label="办案人员" width="120"></el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="120"></el-table-column>

          <el-table-column prop="updateTime" label="更新时间" width="120"></el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button size="small" @click="viewCase(scope.row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>

                <el-button
                  size="small"
                  type="warning"
                  :disabled="scope.row.status !== 'pending'"
                  @click="editCase(scope.row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>

                <el-button
                  size="small"
                  type="success"
                  @click="exportCase(scope.row)">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 编辑案件对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑案件信息"
      width="800px"
      class="edit-dialog">

      <el-form :model="editForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="案件编号">
              <el-input v-model="editForm.caseNumber" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="当事人姓名">
              <el-input v-model="editForm.partyName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="案件类型">
              <el-select v-model="editForm.caseType">
                <el-option label="无证经营" value="unlicensed"></el-option>
                <el-option label="超范围经营" value="beyond_scope"></el-option>
                <el-option label="销售假烟" value="fake_cigarettes"></el-option>
                <el-option label="其他违法" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="办案人员">
              <el-input v-model="editForm.officer"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="案件描述">
              <el-input
                v-model="editForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入案件描述">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出进度对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">

      <div class="export-progress">
        <div class="progress-icon">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="progress-text">{{ exportProgressText }}</div>
        <el-progress
          :percentage="exportProgress"
          :stroke-width="8"
          color="#409EFF">
        </el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  List,
  Download,
  Edit,
  View,
  Loading,
  House,
  Folder
} from '@element-plus/icons-vue'

// 响应式数据
const queryForm = reactive({
  caseNumber: '',
  partyName: '',
  status: '',
  caseType: '',
  dateRange: [],
  officer: ''
})

const caseList = ref([])
const allCases = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const loading = ref(false)

const selectedCases = ref([])

const editDialogVisible = ref(false)
const editForm = reactive({
  id: '',
  caseNumber: '',
  partyName: '',
  caseType: '',
  officer: '',
  description: ''
})

const exportDialogVisible = ref(false)
const exportProgress = ref(0)
const exportProgressText = ref('正在准备导出...')

// 计算属性
const selectedPendingCases = computed(() => {
  return selectedCases.value.filter(item => item.status === 'pending')
})

// 方法定义
const loadMockData = () => {
  const mockCases = []
  const caseTypes = ['unlicensed', 'beyond_scope', 'fake_cigarettes', 'other']
  const statuses = ['pending', 'archived', 'closed']
  const officers = ['张三', '李四', '王五', '赵六', '钱七']
  const partyNames = ['陈某', '刘某', '黄某', '林某', '郑某', '吴某', '周某', '徐某']

  for (let i = 1; i <= 50; i++) {
    const createDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
    const updateDate = new Date(createDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)

    mockCases.push({
      id: i,
      caseNumber: `惠阳烟处（2024）第${String(i).padStart(3, '0')}号`,
      partyName: partyNames[Math.floor(Math.random() * partyNames.length)],
      caseType: caseTypes[Math.floor(Math.random() * caseTypes.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      officer: officers[Math.floor(Math.random() * officers.length)],
      createTime: createDate.toISOString().split('T')[0],
      updateTime: updateDate.toISOString().split('T')[0],
      description: `案件${i}的详细描述信息...`
    })
  }

  allCases.value = mockCases
}

const searchCases = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    let filteredCases = [...allCases.value]

    // 应用筛选条件
    if (queryForm.caseNumber) {
      filteredCases = filteredCases.filter(item =>
        item.caseNumber.includes(queryForm.caseNumber)
      )
    }

    if (queryForm.partyName) {
      filteredCases = filteredCases.filter(item =>
        item.partyName.includes(queryForm.partyName)
      )
    }

    if (queryForm.status) {
      filteredCases = filteredCases.filter(item =>
        item.status === queryForm.status
      )
    }

    if (queryForm.caseType) {
      filteredCases = filteredCases.filter(item =>
        item.caseType === queryForm.caseType
      )
    }

    if (queryForm.officer) {
      filteredCases = filteredCases.filter(item =>
        item.officer.includes(queryForm.officer)
      )
    }

    if (queryForm.dateRange && queryForm.dateRange.length === 2) {
      const [startDate, endDate] = queryForm.dateRange
      filteredCases = filteredCases.filter(item =>
        item.createTime >= startDate && item.createTime <= endDate
      )
    }

    total.value = filteredCases.length

    // 分页
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    caseList.value = filteredCases.slice(start, end)

    loading.value = false
  }, 500)
}

const resetQuery = () => {
  Object.assign(queryForm, {
    caseNumber: '',
    partyName: '',
    status: '',
    caseType: '',
    dateRange: [],
    officer: ''
  })
  currentPage.value = 1
  searchCases()
}

const handleSelectionChange = (selection) => {
  selectedCases.value = selection
}

const viewCase = (row) => {
  ElMessage({
    message: `正在查看案件：${row.caseNumber}`,
    type: 'info'
  })
  // 这里可以跳转到案件详情页面
}

const editCase = (row) => {
  Object.assign(editForm, {
    id: row.id.toString(),
    caseNumber: row.caseNumber,
    partyName: row.partyName,
    caseType: row.caseType,
    officer: row.officer,
    description: row.description
  })
  editDialogVisible.value = true
}

const saveEdit = () => {
  // 模拟保存
  const index = caseList.value.findIndex(item => item.id.toString() === editForm.id)
  if (index !== -1) {
    Object.assign(caseList.value[index], editForm)
    caseList.value[index].updateTime = new Date().toISOString().split('T')[0]
  }

  ElMessage({
    message: '案件信息保存成功',
    type: 'success'
  })

  editDialogVisible.value = false
}

const exportCase = (row) => {
  startExport([row])
}

const batchExport = () => {
  if (selectedCases.value.length === 0) {
    ElMessage({
      message: '请先选择要导出的案件',
      type: 'warning'
    })
    return
  }

  startExport(selectedCases.value)
}

const batchEdit = () => {
  if (selectedPendingCases.value.length === 0) {
    ElMessage({
      message: '请先选择未归档的案件进行编辑',
      type: 'warning'
    })
    return
  }

  ElMessage({
    message: `正在批量编辑 ${selectedPendingCases.value.length} 个案件`,
    type: 'info'
  })
}

const startExport = (cases) => {
  exportDialogVisible.value = true
  exportProgress.value = 0
  exportProgressText.value = '正在准备导出...'

  // 模拟导出进度
  const interval = setInterval(() => {
    exportProgress.value += Math.random() * 20

    if (exportProgress.value < 30) {
      exportProgressText.value = '正在生成Word文档...'
    } else if (exportProgress.value < 60) {
      exportProgressText.value = '正在处理文档格式...'
    } else if (exportProgress.value < 90) {
      exportProgressText.value = '正在压缩文件...'
    } else {
      exportProgressText.value = '即将完成...'
    }

    if (exportProgress.value >= 100) {
      exportProgress.value = 100
      exportProgressText.value = '导出完成！'
      clearInterval(interval)

      setTimeout(() => {
        exportDialogVisible.value = false
        ElMessage({
          message: `成功导出 ${cases.length} 个案件的Word文档`,
          type: 'success'
        })
      }, 1000)
    }
  }, 200)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  searchCases()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  searchCases()
}

const getCaseTypeText = (type) => {
  const typeMap = {
    'unlicensed': '无证经营',
    'beyond_scope': '超范围经营',
    'fake_cigarettes': '销售假烟',
    'other': '其他违法'
  }
  return typeMap[type] || type
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '未归档',
    'archived': '已归档',
    'closed': '已结案'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'archived': 'success',
    'closed': 'info'
  }
  return typeMap[status] || 'info'
}

// 生命周期
onMounted(() => {
  loadMockData()
  searchCases()
})
</script>

<style scoped>
.case-query-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 20px 0;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.header p {
  font-size: 14px;
  opacity: 0.9;
}

.main-container {
  max-width: 1400px;
  margin: 20px auto;
  padding: 0 20px;
}

/* 面包屑样式 */
.breadcrumb {
  background: #ffffff;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #409EFF;
}

.breadcrumb-item.current {
  color: #303133;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #C0C4CC;
  margin: 0 4px;
}

.breadcrumb-icon {
  font-size: 16px;
}

.query-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.query-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.query-form {
  margin-bottom: 20px;
}

.query-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.results-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.results-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

.case-table {
  width: 100%;
}

.status-tag {
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 编辑对话框样式 */
.edit-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

/* 导出进度对话框 */
.export-progress {
  text-align: center;
  padding: 20px;
}

.progress-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .batch-actions {
    justify-content: center;
  }
}
</style>