<template>
  <div class="main-content">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <!-- AI助手介绍 -->
      <div class="ai-assistant">
        <div class="ai-title">🎯 AI文案助手</div>
        <p>基于大模型技术，智能生成专业案卷文书，支持多种文档类型，确保内容准确、格式规范。</p>
      </div>

      <!-- 生成模式选择 -->
      <div class="section-title">
        <el-icon>
          <Setting />
        </el-icon>
        生成模式
      </div>

      <div class="generation-mode">
        <el-radio-group v-model="generationMode" class="mode-selector">
          <el-radio value="single" class="mode-option">
            <div class="mode-content">
              <el-icon>
                <Document />
              </el-icon>
              <span>单独生成</span>
              <p>选择特定文档类型生成</p>
            </div>
          </el-radio>
          <el-radio value="batch" class="mode-option">
            <div class="mode-content">
              <el-icon>
                <Files />
              </el-icon>
              <span>批量生成</span>
              <p>一次性生成所有案卷文件</p>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 文档类型选择 -->
      <div v-if="generationMode === 'single'" class="section-title">
        <el-icon>
          <Document />
        </el-icon>
        选择文档类型
      </div>

      <div v-if="generationMode === 'single'" class="document-types">
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'inquiry' }"
          @click="selectedDocType = 'inquiry'"
        >
          <el-icon>
            <ChatLineRound />
          </el-icon>
          <span>询问笔录</span>
        </div>
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'inspection' }"
          @click="selectedDocType = 'inspection'"
        >
          <el-icon>
            <View />
          </el-icon>
          <span>检查笔录</span>
        </div>
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'seizure' }"
          @click="selectedDocType = 'seizure'"
        >
          <el-icon>
            <Lock />
          </el-icon>
          <span>扣押清单</span>
        </div>
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'penalty' }"
          @click="selectedDocType = 'penalty'"
        >
          <el-icon>
            <Warning />
          </el-icon>
          <span>处罚决定</span>
        </div>
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'notice' }"
          @click="selectedDocType = 'notice'"
        >
          <el-icon>
            <Bell />
          </el-icon>
          <span>告知书</span>
        </div>
        <div
          class="doc-type"
          :class="{ active: selectedDocType === 'hearing' }"
          @click="selectedDocType = 'hearing'"
        >
          <el-icon>
            <Microphone />
          </el-icon>
          <span>听证笔录</span>
        </div>
      </div>

      <!-- 批量文件预览 -->
      <div v-if="generationMode === 'batch'" class="batch-preview">
        <div class="section-title">
          <el-icon>
            <Files />
          </el-icon>
          将生成以下文件
        </div>
        <div class="batch-files">
          <div
            v-for="file in batchFiles"
            :key="file.name"
            class="batch-file-item"
            @click="navigateToFileDetail(file)"
          >
            <div class="file-status">
              <el-icon v-if="file.status === 'pending'" :class="file.icon"></el-icon>
              <el-icon v-else-if="file.status === 'generating'" class="generating-icon">
                <Loading />
              </el-icon>
              <el-icon v-else-if="file.status === 'success'" class="success-icon">
                <CircleCheck />
              </el-icon>
              <el-tooltip
                v-else-if="file.status === 'error'"
                :content="file.error || ''"
                placement="top"
              >
                <el-icon class="error-icon">
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </div>
            <div class="file-info">
              <div class="file-name" :class="{ clickable: file.status === 'success' }">
                {{ file.name }}
              </div>
              <div class="file-type">{{ file.type }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 案件信息表单 -->
      <div class="section-title">
        <el-icon>
          <Edit />
        </el-icon>
        填写案件信息
      </div>

      <div class="form-section">
        <label class="form-label">案件名称</label>
        <div style="display: flex; gap: 10px; align-items: center">
          <el-select
            v-model="caseInfo.name"
            placeholder="请选择或输入案件名称"
            filterable
            allow-create
            clearable
            @change="onCaseNameChange"
            @clear="resetForm"
            style="flex: 1"
          >
            <el-option
              v-for="caseItem in caseData"
              :key="caseItem.name"
              :label="caseItem.name"
              :value="caseItem.name"
            />
          </el-select>
        </div>
      </div>

      <div class="form-section">
        <label class="form-label">当事人信息</label>
        <el-input
          v-model="caseInfo.party"
          placeholder="请输入当事人姓名、身份证号等信息"
          :disabled="isFormDisabled"
        />
      </div>

      <div class="form-section">
        <label class="form-label">案件类型</label>
        <el-select v-model="caseInfo.type" placeholder="请选择案件类型" :disabled="isFormDisabled">
          <el-option label="无证经营" value="unlicensed" />
          <el-option label="销售假冒伪劣" value="counterfeit" />
          <el-option label="超范围经营" value="overscope" />
          <el-option label="其他违法行为" value="other" />
        </el-select>
      </div>

      <div class="form-section">
        <label class="form-label">案件描述</label>
        <el-input
          type="textarea"
          v-model="caseInfo.description"
          placeholder="请详细描述案件经过、违法事实等信息..."
          :disabled="isFormDisabled"
          :rows="4"
        />
      </div>

      <el-button
        class="generate-btn"
        @click="generateDocument"
        :disabled="generating"
        type="primary"
        size="large"
      >
        <span v-if="!generating">
          <span v-if="generationMode === 'single'">🚀 智能生成文书</span>
          <span v-else>📦 批量生成所有文件</span>
        </span>
        <span v-else>
          <span v-if="generationMode === 'single'">⏳ 正在生成中...</span>
          <span v-else>⏳ 正在批量生成中...</span>
        </span>
      </el-button>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="section-title">
        <el-icon>
          <Loading />
        </el-icon>
        生成进度
      </div>

      <div class="progress-section">
        <div class="progress-item">
          <div class="progress-icon" :class="getProgressStatus(0)">
            <el-icon>
              <Edit />
            </el-icon>
          </div>
          <div class="progress-text">
            <div class="progress-title">信息收集</div>
            <div class="progress-desc">分析案件信息和文档类型</div>
          </div>
        </div>

        <div class="progress-item">
          <div class="progress-icon" :class="getProgressStatus(1)">
            <el-icon>
              <Cpu />
            </el-icon>
          </div>
          <div class="progress-text">
            <div class="progress-title">AI智能分析</div>
            <div class="progress-desc">大模型理解案件要素</div>
          </div>
        </div>

        <div class="progress-item">
          <div class="progress-icon" :class="getProgressStatus(2)">
            <el-icon>
              <DocumentCopy />
            </el-icon>
          </div>
          <div class="progress-text">
            <div class="progress-title">文书生成</div>
            <div class="progress-desc">按照法律模板生成文书</div>
          </div>
        </div>

        <div class="progress-item">
          <div class="progress-icon" :class="getProgressStatus(3)">
            <el-icon>
              <Check />
            </el-icon>
          </div>
          <div class="progress-text">
            <div class="progress-title">质量检查</div>
            <div class="progress-desc">校验格式和内容完整性</div>
          </div>
        </div>
      </div>

      <div class="tips-section">
        <div class="tips-title">
          <el-icon>
            <Warning />
          </el-icon>
          使用提示
        </div>
        <ul class="tips-list">
          <li>请确保案件信息填写完整准确</li>
          <li>选择正确的文档类型以获得最佳效果</li>
          <li>生成后可进一步编辑和完善内容</li>
          <li>系统会自动保存生成记录</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Bell,
  ChatLineRound,
  Check,
  CircleCheck,
  Cpu,
  Document,
  DocumentCopy,
  Edit,
  Files,
  Loading,
  Lock,
  Microphone,
  Setting,
  View,
  Warning,
  WarningFilled,
} from '@element-plus/icons-vue';

import { getList } from '@/api/cases/cases';

// 生成模式类型
type GenerationMode = 'single' | 'batch';

// 文档类型
type DocType = 'inquiry' | 'inspection' | 'seizure' | 'penalty' | 'notice' | 'hearing';

// 案件类型
type CaseType = 'unlicensed' | 'counterfeit' | 'overscope' | 'other';

// 文件状态
type FileStatus = 'pending' | 'generating' | 'success' | 'error';

// 进度状态
type ProgressStatus = 'completed' | 'current' | 'pending';

// 案件信息接口
interface CaseInfo {
  name: string;
  party: string;
  type: CaseType | '';
  description: string;
}

// 预定义案件接口
interface PredefinedCase {
  name: string;
  party: string;
  type: CaseType;
  description: string;
}

// 批量文件接口
interface BatchFile {
  name: string;
  type: string;
  icon: string;
  status: FileStatus;
  error: string | null;
}

// 响应式数据
const generationMode = ref<GenerationMode>('single');
const selectedDocType = ref<DocType | ''>('');
const generating = ref(false);
const currentStep = ref(1);
const isFormDisabled = ref(false);

// 案件信息
const caseInfo = reactive<CaseInfo>({
  name: '',
  party: '',
  type: '',
  description: '',
});

// 批量文件数据
const batchFiles = ref<BatchFile[]>([
  { name: '举报记录表', type: '记录类', icon: 'el-icon-document', status: 'pending', error: null },
  {
    name: '立案（不予立案）报告表',
    type: '报告类',
    icon: 'el-icon-folder-opened',
    status: 'pending',
    error: null,
  },
  {
    name: '指定管辖通知书',
    type: '通知类',
    icon: 'el-icon-message',
    status: 'pending',
    error: null,
  },
  {
    name: '检查（勘验）笔录',
    type: '笔录类',
    icon: 'el-icon-view',
    status: 'pending',
    error: null,
  },
  {
    name: '先行登记保存批准书或通知书',
    type: '批准类',
    icon: 'el-icon-document-checked',
    status: 'pending',
    error: null,
  },
  {
    name: '询问笔录',
    type: '笔录类',
    icon: 'el-icon-chat-line-round',
    status: 'pending',
    error: null,
  },
  {
    name: '抽样取证物品清单',
    type: '清单类',
    icon: 'el-icon-tickets',
    status: 'pending',
    error: null,
  },
  {
    name: '涉案烟草专卖品核价表',
    type: '核价类',
    icon: 'el-icon-money',
    status: 'pending',
    error: null,
  },
  {
    name: '调查终结报告',
    type: '报告类',
    icon: 'el-icon-document',
    status: 'pending',
    error: null,
  },
  {
    name: '行政处罚事先告知书',
    type: '告知类',
    icon: 'el-icon-bell',
    status: 'pending',
    error: null,
  },
  {
    name: '案件集体讨论记录',
    type: '记录类',
    icon: 'el-icon-chat-dot-round',
    status: 'pending',
    error: null,
  },
  {
    name: '行政处罚决定书',
    type: '决定类',
    icon: 'el-icon-warning',
    status: 'pending',
    error: null,
  },
  { name: '送达回证', type: '证明类', icon: 'el-icon-postcard', status: 'pending', error: null },
  { name: '罚没物品移交单', type: '移交类', icon: 'el-icon-box', status: 'pending', error: null },
  {
    name: '延期/分期缴纳罚款通知书',
    type: '通知类',
    icon: 'el-icon-timer',
    status: 'pending',
    error: null,
  },
  {
    name: '案件调查终结报告',
    type: '报告类',
    icon: 'el-icon-finished',
    status: 'pending',
    error: null,
  },
  {
    name: '结案报告表',
    type: '报告类',
    icon: 'el-icon-circle-check',
    status: 'pending',
    error: null,
  },
  {
    name: '卷宗封面与目录',
    type: '目录类',
    icon: 'el-icon-folder',
    status: 'pending',
    error: null,
  },
  { name: '电子数据提取笔录', type: '笔录类', icon: 'el-icon-cpu', status: 'pending', error: null },
  { name: '案件移送函', type: '函件类', icon: 'el-icon-position', status: 'pending', error: null },
  {
    name: '听证相关文书',
    type: '听证类',
    icon: 'el-icon-microphone',
    status: 'pending',
    error: null,
  },
  {
    name: '当事人身份证复印件',
    type: '证件类',
    icon: 'el-icon-user',
    status: 'pending',
    error: null,
  },
]);

// 方法
const validateForm = (): boolean => {
  if (!caseInfo.name.trim()) {
    ElMessage.warning('请输入案件名称');
    return false;
  }
  if (!caseInfo.party.trim()) {
    ElMessage.warning('请输入当事人信息');
    return false;
  }
  if (!caseInfo.type) {
    ElMessage.warning('请选择案件类型');
    return false;
  }
  if (!caseInfo.description.trim()) {
    ElMessage.warning('请输入案件描述');
    return false;
  }
  if (generationMode.value === 'single' && !selectedDocType.value) {
    ElMessage.warning('请选择文档类型');
    return false;
  }
  return true;
};

const onCaseNameChange = (selectedName: string) => {
  // 查找选中的预定义案件
  const selectedCase = caseData.value.find(caseItem => caseItem.name === selectedName);

  if (selectedCase) {
    // 如果选择的是预定义案件，自动填写其他信息并设为不可编辑
    caseInfo.party = selectedCase.party;
    caseInfo.type = selectedCase.type;
    caseInfo.description = selectedCase.description;
    isFormDisabled.value = true;
  } else {
    // 如果是自定义输入，清空其他字段并设为可编辑
    caseInfo.party = '';
    caseInfo.type = '';
    caseInfo.description = '';
    isFormDisabled.value = false;

    if (selectedName && selectedName.trim()) {
      ElMessage.info('请手动填写案件信息');
    }
  }
};

const resetForm = () => {
  // 重置表单状态，允许重新编辑
  caseInfo.name = '';
  caseInfo.party = '';
  caseInfo.type = '';
  caseInfo.description = '';
  isFormDisabled.value = false;
};

const navigateToFileDetail = (file: BatchFile) => {
  if (file.status === 'success') {
    // 跳转到文件详情页
    ElMessage.info(`正在打开 ${file.name} 详情页...`);
    setTimeout(() => {
      // 这里可以使用 router.push 跳转到详情页
      // window.location.href = `case-detail.html?name=${encodeURIComponent(file.name)}`
    }, 500);
  }
};

const simulateBatchGeneration = () => {
  // 模拟批量生成过程
  const files = [...batchFiles.value];
  let currentIndex = 0;

  const generateNext = () => {
    if (currentIndex >= files.length) {
      return;
    }

    const file = files[currentIndex];
    file.status = 'generating';

    // 模拟生成时间（1-3秒）
    const generateTime = Math.random() * 2000 + 1000;

    setTimeout(() => {
      // 90%成功率，10%失败率
      if (Math.random() < 0.9) {
        file.status = 'success';
      } else {
        file.status = 'error';
        file.error = `生成${file.name}时发生错误：网络连接超时，请重试`;
      }

      currentIndex++;
      generateNext();
    }, generateTime);
  };

  generateNext();
};

const getProgressStatus = (step: number): ProgressStatus => {
  if (step < currentStep.value) {
    return 'completed';
  } else if (step === currentStep.value && generating.value) {
    return 'current';
  } else {
    return 'pending';
  }
};

const generateDocument = () => {
  if (!validateForm()) {
    return;
  }

  generating.value = true;
  currentStep.value = 0;

  if (generationMode.value === 'batch') {
    // 重置所有文件状态
    batchFiles.value.forEach(file => {
      file.status = 'pending';
      file.error = null;
    });

    // 启动批量生成模拟
    simulateBatchGeneration();
  }

  // 模拟生成过程
  const steps = [1000, 2000, 1500, 1000];
  let totalTime = 0;

  steps.forEach((time, index) => {
    totalTime += time;
    setTimeout(() => {
      currentStep.value = index + 1;
      if (index === steps.length - 1) {
        generating.value = false;
        if (generationMode.value === 'single') {
          ElMessage.success('文书生成完成！');
        } else {
          const successCount = batchFiles.value.filter(file => file.status === 'success').length;
          ElMessage.success(`批量生成完成！共生成${successCount}个文件`);
        }
        // 这里可以跳转到预览页面或下载文件
      }
    }, totalTime);
  });
};

// 获取案件数据
const caseData = ref<PredefinedCase[]>([]);
const getCaseData = async (name: string) => {
  try {
    const res = await getList({ name: name });
    caseData.value = res.data.data.records.map(item => {
      return {
        ...item,
        party: `${item.litigantName}，${item.litigantSex}，${item.litigantNation}，身份证号：${item.litigantIdNumber}`,
      };
    });
  } catch (error) {
    console.error('获取案件数据失败:', error);
  }
};
onMounted(() => {
  getCaseData();
});
</script>

<style scoped>
/* 全局样式优化 */
:deep(.el-card) {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.breadcrumb :deep(.el-breadcrumb__item) {
  font-weight: 500;
}

.breadcrumb :deep(.el-breadcrumb__inner) {
  color: #64748b;
  transition: color 0.2s ease;
}

.breadcrumb :deep(.el-breadcrumb__inner:hover) {
  color: #667eea;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 420px;
  gap: 24px;
  margin: 10px;
}

.left-panel,
.right-panel {
  height: calc(100vh - 140px);
  box-sizing: border-box;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: scroll;
}

/* 章节标题样式 */
.section-title {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  padding-bottom: 12px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-title .el-icon {
  color: #667eea;
  font-size: 26px;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
}

/* AI助手卡片样式 */
.ai-assistant {
  background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
  border: 2px solid #667eea;
  border-radius: 16px;
  padding: 28px;
  margin-bottom: 28px;
  position: relative;
  overflow: hidden;
}

.ai-assistant::before {
  content: '🤖';
  position: absolute;
  top: -8px;
  right: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 8px 12px;
  border-radius: 24px;
  font-size: 18px;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ai-assistant::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.ai-title {
  color: #667eea;
  font-weight: 700;
  margin-bottom: 16px;
  font-size: 18px;
  position: relative;
  z-index: 1;
}

.ai-assistant p {
  color: #475569;
  line-height: 1.7;
  margin: 0;
  position: relative;
  z-index: 1;
  font-weight: 400;
}

/* 生成模式选择样式 */
.generation-mode {
  height: 80px;
  margin-bottom: 28px;
  margin-top: 54px;
}

.mode-selector {
  display: flex;
  gap: 15px;
  width: 100%;
}

.mode-option {
  flex: 1;
  margin: 0 !important;
}

.mode-option :deep(.el-radio__input) {
  display: none !important;
}

.mode-option :deep(.el-radio__label) {
  padding: 0 !important;
  width: 100% !important;
  display: block !important;
}

.mode-content {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.mode-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mode-content:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15);
  border-color: #667eea;
}

.mode-content:hover::before {
  opacity: 1;
}

.mode-option.is-checked .mode-content {
  border-color: #667eea !important;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.mode-content .el-icon {
  font-size: 24px;
  color: #667eea;
  margin-bottom: 6px;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
  position: relative;
  z-index: 1;
}

.mode-content span {
  font-size: 14px;
  font-weight: 700;
  color: #1e293b;
  display: block;
  margin-bottom: 2px;
  position: relative;
  z-index: 1;
}

.mode-content p {
  font-size: 12px;
  color: #64748b;
  margin: 0;
  line-height: 1.2;
  position: relative;
  z-index: 1;
}

.document-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.doc-type {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.doc-type::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.doc-type:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
}

.doc-type:hover::before {
  opacity: 1;
}

.doc-type.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}

.doc-type .el-icon {
  font-size: 28px;
  margin-bottom: 12px;
  display: block;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.doc-type span {
  font-size: 15px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* 表单样式 */
.form-section {
  margin-bottom: 28px;
}

.form-label {
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
  display: block;
  font-size: 16px;
}

/* 生成按钮样式 */
.generate-btn {
  width: 100%;
  height: 56px;
  margin-top: 24px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.generate-btn:active {
  transform: translateY(0);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 12px;
}

:deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

:deep(.el-textarea__inner:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

:deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 批量文件预览样式 */
.batch-preview {
  margin-top: 24px;
  margin-bottom: 32px;
  padding-top: 12px;
}

.batch-files {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 16px;
  max-height: 320px;
  overflow-y: auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.batch-files::-webkit-scrollbar {
  width: 8px;
}

.batch-files::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
}

.batch-files::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.batch-files::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.batch-file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.batch-file-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.batch-file-item:last-child {
  margin-bottom: 0;
}

.batch-file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.batch-file-item:hover::before {
  opacity: 1;
}

/* 文件状态样式 */
.file-status {
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.file-status .el-icon {
  font-size: 18px;
  display: block;
  line-height: 1;
  position: relative;
  z-index: 1;
}

.generating-icon {
  color: #667eea !important;
  animation: rotate 2s linear infinite;
}

.success-icon {
  color: #10b981 !important;
}

.error-icon {
  color: #ef4444 !important;
}

.file-status:has(.generating-icon) {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.file-status:has(.success-icon) {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.file-status:has(.error-icon) {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.file-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.file-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 15px;
  position: relative;
  z-index: 1;
}

.file-name.clickable {
  color: #667eea;
  text-decoration: underline;
  cursor: pointer;
}

.file-name.clickable:hover {
  color: #4f46e5;
}

.file-type {
  font-size: 12px;
  color: #64748b;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 4px 12px;
  border-radius: 16px;
  white-space: nowrap;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 进度显示样式 */
.progress-section {
  margin-bottom: 32px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 18px 0;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-item::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 58px;
  width: 2px;
  height: calc(100% - 40px);
  background: linear-gradient(180deg, #e2e8f0 0%, transparent 100%);
}

.progress-item:last-child::before {
  display: none;
}

.progress-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.progress-icon.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.progress-icon.current {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: pulse 2s infinite;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.progress-icon.pending {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #64748b;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
  }
}

.progress-text {
  flex: 1;
}

.progress-title {
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 6px;
  font-size: 16px;
}

.progress-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

/* 提示区域样式 */
.tips-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 16px;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.tips-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(20px, -20px);
}

.tips-title {
  color: #d97706;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.tips-list {
  list-style: none;
  color: #92400e;
  font-size: 14px;
  line-height: 1.7;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 1;
}

.tips-list li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 20px;
  font-weight: 500;
}

.tips-list li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr 380px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 14px;
  }

  .left-panel,
  .right-panel {
    padding: 24px;
    border-radius: 16px;
  }

  .document-types {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .mode-selector {
    flex-direction: column;
    gap: 12px;
  }

  .mode-content {
    padding: 10px;
    height: 70px;
  }

  .batch-files {
    max-height: 280px;
    padding: 12px;
  }

  .batch-file-item {
    padding: 10px 12px;
    min-height: 44px;
  }

  .progress-item {
    gap: 14px;
    padding: 14px 0;
  }

  .progress-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .left-panel,
  .right-panel {
    padding: 20px;
  }

  .document-types {
    grid-template-columns: 1fr;
  }

  .mode-content {
    height: 65px;
    padding: 8px;
  }

  .mode-content .el-icon {
    font-size: 28px;
  }

  .mode-content span {
    font-size: 16px;
  }

  .generate-btn {
    height: 52px;
    font-size: 15px;
  }
}
</style>