<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专卖案件文书大模型智办系统</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #337ecc 0%, #5daf34 100%);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }
        
        ::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }
        
        /* Firefox 滚动条样式 */
        * {
            scrollbar-width: thin;
            scrollbar-color: #409EFF #f1f1f1;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            min-height: 100vh;
            padding-bottom: 200px;
        }
        
        .header {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .main-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
            position: relative;
        }

        .left-sidebar {
            width: 350px;
            flex-shrink: 0;
            transition: all 0.3s ease;
            height: calc(100vh - 140px);
            position: fixed;
            top: 120px;
            left: 20px;
        }

        .left-sidebar.collapsed {
            width: 60px;
        }

        .left-sidebar.collapsed ~ .main-container .main-content {
            margin-left: 100px;
        }

        .main-content {
            flex: 1;
            min-width: 0;
        }

        .main-content.right-sidebar-collapsed {
            margin-right: 60px;
        }


        
        /* 面包屑样式 */
        .breadcrumb {
            background: #ffffff;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        .stats-overview {
            width: 356px;
        }
        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;
            text-decoration: none;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: #409EFF;
        }

        .breadcrumb-item.current {
            color: #303133;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #C0C4CC;
            margin: 0 4px;
        }

        .breadcrumb-icon {
            font-size: 16px;
        }
        
        /* 智能模式区域样式 */
        .smart-mode-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 16px;
            padding: 32px 24px;
            margin: 24px 0;
            border: 1px solid #d1ecf1;
            box-shadow: 0 6px 30px rgba(64, 158, 255, 0.15);
            position: relative;
            overflow: hidden;
        }
        
        .smart-mode-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #409EFF 0%, #67C23A 50%, #E6A23C 100%);
        }
        
        .smart-mode-header {
            text-align: center;
            margin-bottom: 28px;
            position: relative;
            z-index: 1;
        }
        
        .smart-mode-header h2 {
            font-size: 24px;
            color: #303133;
            margin-bottom: 8px;
            font-weight: 700;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .smart-mode-header p {
            font-size: 16px;
            color: #606266;
            margin: 0;
            font-weight: 500;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: auto auto;
            gap: 20px;
        }
        
        .function-grid .analysis-card {
            grid-column: 1 / -1;
            max-width: 400px;
            margin: 0 auto;
        }
        
        /* 主要功能区域布局 */
        .main-features-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 16px;
            width: 720px;
            margin-left: auto;
            margin-right: auto;
        }

        .system-functions-row {
            width: 100%;
        }

        .bottom-sections-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            width: 720px;
        }
        
        .feature-section {
            background: white;
            height: 150px;
            padding: 10px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .feature-section.system-functions {
            height: auto;
            min-height: 120px;
        }

        
        .feature-section.system-overview {
            display: flex;
            flex-direction: column;
        }
        
        .feature-section.system-overview .function-cards-container {
            display: flex;
            gap: 5px;
            justify-content: space-between;
            flex-wrap: wrap;
            flex: 1;
            align-items: stretch;
        }
        
        .feature-section.system-functions .function-cards-container {
            display: flex;
            gap: 15px;
            justify-content: space-between;
            flex-wrap: nowrap;
            flex: 1;
            align-items: stretch;
        }
        
        .feature-section .stats-grid {
            display: flex;
            gap: 10px;
            justify-content: space-between;
            flex: 1;
            align-items: stretch;
        }
        
        .feature-section h3 {
            color: #303133;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .function-card {
            background: white;
            border-radius: 12px;
            padding: 8px 8px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            height: 100px;
        }
        
        .feature-section .function-card {
            flex: 1;
            margin-bottom: 0;
            height: auto;
            min-height: 80px;
            min-width: 0; /* 允许卡片在必要时缩小 */
        }
        
        .feature-section.system-functions .function-card {
            flex: 1;
            margin-bottom: 0;
            height: auto;
            min-height: 100px;
            min-width: 150px;
            max-width: none;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border-color: #409EFF;
        }
        
        .function-icon {
            font-size: 24px;
            color: #409EFF;
            position: relative;
            z-index: 2;
        }
        
        .generate-icon {
            color: #409EFF;
        }
        
        .review-icon {
            color: #67C23A;
        }
        
        .analysis-icon {
            color: #E6A23C;
        }
        
        .query-icon {
            color: #722ED1;
        }

        .chat-icon {
            color: #FF6B6B;
        }
        
        .card-decoration {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            opacity: 0.4;
        }
        
        .decoration-icon {
            font-size: 22px;
            color: #909399;
        }
        
        .feature-tags {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        
        .function-title {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
        }
        
        .function-description {
            font-size: 13px;
            color: #909399;
            line-height: 1.5;
            margin-bottom: 0;
            display: none;
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            white-space: nowrap;
            z-index: 1000;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .function-description::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid rgba(0, 0, 0, 0.8);
        }
        
        .function-card:hover .function-description {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
        
        .function-button {
            width: 100%;
            height: 44px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
        }
        
        .stats-section {
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
        }
        
        .stat-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 80px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
        }
        
        .process-section {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #e4e7ed;
        }
        
        .process-flow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            flex-wrap: wrap;
            padding: 20px 0;
        }
        
        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            min-width: 180px;
        }
        
        .flow-icon-container {
            margin-bottom: 20px;
            position: relative;
        }
        
        .flow-icon {
            width: 80px;
            height: 80px;
            position: relative;
            margin: 0 auto;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .flow-icon:hover {
            transform: translateY(-5px) scale(1.05);
        }
        
        .icon-3d {
            position: absolute;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
        }
        
        .icon-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 12px;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .layer-1 {
            transform: translateZ(0px);
            background: linear-gradient(135deg, #409EFF 0%, #5DADE2 100%);
            box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
        }
        
        .layer-2 {
            transform: translateZ(-5px);
            background: linear-gradient(135deg, #5DADE2 0%, #85C1E9 100%);
            opacity: 0.8;
            font-size: 18px;
        }
        
        .layer-3 {
            transform: translateZ(-10px);
            background: linear-gradient(135deg, #85C1E9 0%, #AED6F1 100%);
            opacity: 0.6;
            font-size: 16px;
        }
        
        .data-icon .icon-layer {
            background: linear-gradient(135deg, #3498DB 0%, #5DADE2 100%);
        }
        
        .design-icon .icon-layer {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
        }
        
        .usage-icon .icon-layer {
            background: linear-gradient(135deg, #67C23A 0%, #85D8CE 100%);
        }
        
        .manage-icon .icon-layer {
            background: linear-gradient(135deg, #E6A23C 0%, #F7DC6F 100%);
        }
        
        .main-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 32px;
            color: white;
            z-index: 10;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .flow-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
            line-height: 1.3;
        }
        
        .flow-subtitle {
            font-size: 14px;
            color: #606266;
            line-height: 1.4;
        }
        
        .flow-arrow {
            position: absolute;
            right: -35px;
            top: 40px;
            z-index: 5;
        }
        
        .flow-step:last-child .flow-arrow {
            display: none;
        }
        
        .flow-arrow svg {
            filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
        }
        
        .activity-status-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
        }
        
        .recent-activity, .system-status {
            background: white;
            padding: 10px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
        }
            .recent-activity {
            max-height: 400px;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .recent-activity h3 {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            margin: 0 0 15px 0;
            padding: 0 0 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .recent-activity .el-timeline {
            flex: 1;
            max-height: 320px;
            overflow-y: auto;
            padding-right: 10px;
        }
        
        .recent-activity .el-timeline-item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 8px;
        }
        
        .recent-activity .el-timeline-item__content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            line-height: 1.4;
        }
        

        
        .system-status h3 {
            color: #303133;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .recent-activity h3 {
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }
        
        .status-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .status-text {
            font-size: 14px;
            color: #606266;
        }
        
        /* 法规更新公告样式 */
        .regulation-updates {
            width: 356px;
            background: white;
            padding: 8px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            max-height: 150px;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .title-span{
            font-size: 15px;
        }
        .regulation-header-row {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 0 15px 0;
            padding: 0 0 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .regulation-updates h3 {
            color: #303133;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .view-more-link {
            color: #409EFF;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
        }
        
        .view-more-link:hover {
            color: #337ecc;
            text-decoration: underline;
        }
        
        .regulation-items {
            flex: 1;
            overflow-y: auto;
            padding-right: 10px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .regulation-item {
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .regulation-item:hover {
            background: #f0f9ff;
            transform: translateX(5px);
        }
        

        
        .regulation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .regulation-date {
            font-size: 12px;
            color: #909399;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .regulation-title {
            font-size: 14px;
            color: #303133;
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 4px;
            cursor: pointer;
            position: relative;
        }
        
        .regulation-title:hover::after {
            content: attr(data-title);
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            white-space: normal;
            max-width: 400px;
            word-wrap: break-word;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: fadeIn 0.3s ease-in-out;
            line-height: 1.5;
            text-align: center;
        }
        
        .view-more {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ebeef5;
        }
        
        .view-more .el-button {
            padding: 10px 20px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .view-more .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        /* 滚动公告栏样式 */
        .announcement-bar {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 8px;
            padding: 12px 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #409EFF;
            overflow: hidden;
        }
        
        .announcement-icon {
            flex-shrink: 0;
            margin-right: 15px;
            background-color: #409EFF;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .announcement-content {
            flex-grow: 1;
            overflow: hidden;
            position: relative;
        }
        
        .announcement-scroll {
            white-space: nowrap;
            animation: scroll-left 30s linear infinite;
            color: #303133;
            font-size: 14px;
        }
        
        .announcement-separator {
            display: inline-block;
            margin: 0 15px;
            color: #909399;
        }
        
        @keyframes scroll-left {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        

        
        @media (max-width: 768px) {
             .main-features-grid {
                 grid-template-columns: 1fr;
                 max-width: 800px;
                 left: 0;
                 transform: none;
                 margin-left: 20px;
                 margin-right: 20px;
             }
             
             .process-flow {
                  flex-direction: column;
                  align-items: center;
                  gap: 40px;
              }
              
              .flow-step {
                  max-width: 280px;
              }
              
              .flow-arrow {
                  display: none;
              }
              
              .flow-icon {
                  width: 70px;
                  height: 70px;
              }
              
              .main-icon {
                  font-size: 28px;
              }
             
             .function-grid {
                 grid-template-columns: repeat(2, 1fr);
                 gap: 15px;
             }
             
             .smart-mode-section {
                 padding: 20px;
                 margin: 20px 0;
             }
             
             .smart-mode-header h2 {
                 font-size: 20px;
             }
             
             .feature-section {
                 padding: 10px;
             }
         }
         
         @media (max-width: 480px) {
             .function-grid {
                 grid-template-columns: 1fr;
                 max-width: 400px;
                 margin: 0 auto;
             }
         }
         
         /* 示例问答区域样式 */
         .qa-examples-section {
             margin: 0;
             padding: 0;
             flex: 1;
             display: flex;
             flex-direction: column;
             overflow: hidden;
         }


         .qa-examples-header {
             padding: 12px 16px 8px;
             border-bottom: 1px solid #e8e8e8;
             background: #f0f2f5;
             flex-shrink: 0;
         }

         .qa-examples-header h4 {
             margin: 0;
             font-size: 14px;
             color: #606266;
             font-weight: 600;
         }

         .qa-examples-list {
             padding: 0;
             flex: 1;
             overflow-y: auto;
         }

         .qa-example-item {
             padding: 12px 16px;
             border-bottom: none;
             display: flex;
             flex-direction: column;
             gap: 8px;
         }

         .qa-example-item:last-child {
             border-bottom: none;
         }

         .qa-example-question,
         .qa-example-answer {
             display: flex;
             align-items: flex-start;
             gap: 6px;
             max-width: 90%;
         }

         .qa-example-question {
             align-self: flex-end;
             flex-direction: row-reverse;
         }

         .qa-example-answer {
             align-self: flex-start;
             margin-bottom: 0;
         }

         .qa-example-question .qa-bubble,
         .qa-example-answer .qa-bubble {
             padding: 8px 12px;
             border-radius: 12px;
             font-size: 13px;
             line-height: 1.4;
         }

         .qa-example-question .qa-bubble {
             background: linear-gradient(135deg, #409EFF, #337ecc);
             color: white;
             border-bottom-right-radius: 4px;
         }

         .qa-example-answer .qa-bubble {
             background: #f8f9fa;
             color: #303133;
             border-bottom-left-radius: 4px;
             border: 1px solid #e4e7ed;
         }

         .qa-examples-section .qa-icon {
             width: 20px;
             height: 20px;
             font-size: 12px;
         }

         .qa-examples-list::-webkit-scrollbar {
             width: 6px;
         }

         .qa-examples-list::-webkit-scrollbar-track {
             background: #f1f1f1;
             border-radius: 3px;
         }

         .qa-examples-list::-webkit-scrollbar-thumb {
             background: #c1c1c1;
             border-radius: 3px;
         }

         .qa-examples-list::-webkit-scrollbar-thumb:hover {
             background: #a8a8a8;
         }
         
         /* 问答区域样式 */
         .qa-section {
             position: fixed;
             bottom: 20px;
             left: 50%;
             transform: translateX(-50%);
             z-index: 1000;
             max-width: 720px;
             width: calc(100% - 40px);
             padding: 0;
             height: 450px;
             display: flex;
             flex-direction: column;
             overflow: hidden; /* 防止内容溢出 */
         }
         

         
         .qa-input-container {
             background: white;
             border-radius: 0 0 24px 24px;
             padding: 16px;
             box-shadow: none;
             border: none;
             flex-shrink: 0;
             position: sticky;
             bottom: 0;
             z-index: 10;
         }
         
         .qa-input-wrapper {
             position: relative;
             border: 1px solid #e4e7ed;
             border-radius: 16px;
             background: #f8f9fa;
             padding: 12px 16px;
             transition: all 0.3s ease;
         }
         
         .qa-input-wrapper:focus-within {
             border-color: #409EFF;
             box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
         }
        
         .qa-input .el-textarea__inner {
             border: none;
             font-size: 14px;
             line-height: 1.6;
             padding: 0;
             background: transparent;
             box-shadow: none;
         }
         
         .qa-input .el-textarea__inner:focus {
             border: none;
             box-shadow: none;
         }
         
         .qa-suggestions {
             display: flex;
             flex-wrap: wrap;
             gap: 8px;
             margin: 12px 0 8px 0;
         }
         
         .qa-actions {
             display: flex;
             justify-content: flex-end;
             align-items: flex-start;
             margin-top: 12px;
             padding-top: 8px;
             border-top: 1px solid #e8e8e8;
         }
         
         .suggestion-tag {
             cursor: pointer;
             transition: all 0.3s ease;
             border-radius: 12px;
             font-size: 12px;
             padding: 4px 8px;
             border: 1px solid #e4e7ed;
             background: #f8f9fa;
             color: #666;
         }
         
         .suggestion-tag:hover {
             background-color: #409EFF;
             color: white;
             border-color: #409EFF;
             transform: translateY(-1px);
         }
         
         .qa-buttons {
             display: flex;
             gap: 8px;
             flex-shrink: 0;
             align-items: center;
         }
         
         .qa-buttons .el-button {
             border-radius: 16px;
             padding: 8px 16px;
             font-size: 13px;
         }
         
         .qa-buttons .el-button--primary {
             background: #409EFF;
             border-color: #409EFF;
         }
         
         /* 上传按钮样式 */
         .qa-buttons .upload-demo {
             display: inline-block;
         }
         
         .upload-btn {
             color: #666;
             padding: 4px 8px;
             border-radius: 12px;
             transition: all 0.3s ease;
             font-size: 12px;
             border: 1px solid #e4e7ed;
             background: #f8f9fa;
         }
         
         .upload-btn:hover {
             background-color: #409EFF;
             color: white;
             border-color: #409EFF;
         }
         
         .uploaded-files {
             display: flex;
             flex-wrap: wrap;
             gap: 8px;
             margin: 12px 0 8px 0;
         }
         
         .file-item {
             display: flex;
             align-items: center;
             gap: 6px;
             background: white;
             padding: 6px 10px;
             border-radius: 6px;
             border: 1px solid #e4e7ed;
             font-size: 12px;
             color: #666;
         }
         
         .file-icon {
             font-size: 14px;
             color: #409EFF;
         }
         
         .file-name {
             max-width: 120px;
             overflow: hidden;
             text-overflow: ellipsis;
             white-space: nowrap;
         }
         
         .remove-btn {
             color: #f56c6c;
             padding: 2px;
             margin-left: 4px;
         }
         
         .remove-btn:hover {
             background-color: #fef0f0;
         }
         
         .qa-history {
             background: #f8f9fa;
             border-radius: 8px;
             padding: 16px 12px;
             height: 100%;
             display: flex;
             flex-direction: column;
             position: relative;
         }
         
         .left-sidebar.collapsed .qa-history {
             padding: 10px;
         }
         
         .left-sidebar.collapsed .qa-history-content {
             display: none;
         }
         
         .sidebar-toggle {
             position: absolute;
             top: 10px;
             right: -15px;
             width: 30px;
             height: 30px;
             background: #409EFF;
             border: none;
             border-radius: 50%;
             color: white;
             cursor: pointer;
             display: flex;
             align-items: center;
             justify-content: center;
             box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
             transition: all 0.3s ease;
             z-index: 100;
         }
         
         .sidebar-toggle:hover {
             background: #337ecc;
             transform: scale(1.1);
         }
         
         .sidebar-toggle i {
             font-size: 14px;
             transition: transform 0.3s ease;
         }
         
         .left-sidebar.collapsed .sidebar-toggle i {
              transform: rotate(180deg);
          }
          
          .no-history {
             text-align: center;
             color: #999;
             font-size: 13px;
             padding: 20px 10px;
             flex: 1;
             display: flex;
             align-items: center;
             justify-content: center;
             flex-direction: column;
         }
          
          .no-history p {
              margin: 0;
          }
          
          .left-sidebar.collapsed .no-history {
              display: none;
          }

          /* 右侧边栏样式 */
          .right-sidebar {
              background: #f8f9fa;
              border-radius: 8px;
              padding: 16px 12px;
              display: flex;
              flex-direction: column;
              position: fixed;
              top: 120px;
              right: 20px;
              width: 300px;
              height: calc(100vh - 140px);
              transition: all 0.3s ease;
              z-index: 100;
          }

          .right-sidebar.collapsed {
              padding: 10px;
              width: 40px;
          }

          .right-sidebar.collapsed .recent-activity-content {
              display: none;
          }

          .right-sidebar-toggle {
              position: absolute;
              top: 10px;
              left: -15px;
              width: 30px;
              height: 30px;
              background: #409EFF;
              border: none;
              border-radius: 50%;
              color: white;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
              transition: all 0.3s ease;
              z-index: 100;
          }

          .right-sidebar-toggle:hover {
              background: #337ecc;
              transform: scale(1.1);
          }

          .right-sidebar-toggle i {
              font-size: 14px;
              transition: transform 0.3s ease;
          }

          .right-sidebar.collapsed .right-sidebar-toggle i {
              transform: rotate(180deg);
          }

          .recent-activity-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16px;
              padding-bottom: 8px;
              flex-shrink: 0;
          }

          .recent-activity-header h4 {
              font-size: 16px;
              color: #303133;
              margin: 0;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;
          }

          .recent-activity-list {
              flex: 1;
              overflow-y: auto;
              padding-right: 8px;
          }

          .recent-activity-list .el-timeline {
              padding-left: 0;
          }

          .recent-activity-list .el-timeline-item {
              margin-bottom: 12px;
          }

          .recent-activity-list .el-timeline-item__content {
              font-size: 13px;
              line-height: 1.4;
              color: #606266;
          }

          .recent-activity-list::-webkit-scrollbar {
              width: 4px;
          }

          .recent-activity-list::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 2px;
          }

          .recent-activity-list::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 2px;
          }

          .recent-activity-list::-webkit-scrollbar-thumb:hover {
              background: #a8a8a8;
          }
          
          /* 响应式设计 */
           @media (max-width: 768px) {
               .main-container {
                   flex-direction: column;
               }
               
               .left-sidebar {
                   width: 100%;
                   order: 2;
                   position: relative;
                   height: auto;
                   top: auto;
                   left: auto;
               }
               
               .main-content {
                   order: 1;
                   margin-left: 0;
               }
               
               .sidebar-toggle {
                   display: none;
               }
               
               .qa-section {
                   bottom: 10px;
                   max-width: calc(100% - 20px);
                   width: 720px;
                   left: 50%;
                   transform: translateX(-50%);
                   border-radius: 20px;
               }
               
               body {
                   padding-bottom: 180px;
               }
               
               .qa-upload-section {
                   margin: 10px 0;
                   padding: 10px;
               }
               
               .upload-buttons {
                   gap: 10px;
               }
               
               .file-item {
                   font-size: 11px;
               }
               
               .file-name {
                   max-width: 80px;
               }
           }
         
         .qa-history-content {
             flex: 1;
             display: flex;
             flex-direction: column;
             overflow: hidden;
         }
         
         .qa-history-header {
             display: flex;
             justify-content: space-between;
             align-items: center;
             margin-bottom: 16px;
             padding-bottom: 8px;
             flex-shrink: 0;
         }

         .qa-history-header h4 {
             font-size: 14px;
             color: #666;
             margin: 0;
             font-weight: 500;
         }
         
         .qa-history-header .el-button {
             color: #999;
             font-size: 11px;
             padding: 2px 6px;
             border-radius: 4px;
             transition: all 0.2s ease;
             border: none;
             background: transparent;
         }

         .qa-history-header .el-button:hover {
             background: #e8e8e8;
             color: #666;
         }

         .qa-history-header .el-button .el-icon {
             margin-right: 2px;
         }
         
         .qa-history-list {
             display: flex;
             flex-direction: column;
             flex: 1;
             overflow-y: auto;
             padding-right: 8px;
             padding-left: 4px;
         }

         .qa-history-list::-webkit-scrollbar {
             width: 6px;
         }

         .qa-history-list::-webkit-scrollbar-track {
             background: #f1f1f1;
             border-radius: 3px;
         }

         .qa-history-list::-webkit-scrollbar-thumb {
             background: #c1c1c1;
             border-radius: 3px;
         }

         .qa-history-list::-webkit-scrollbar-thumb:hover {
             background: #a8a8a8;
         }

         .conversation-item {
             padding: 4px 6px;
             margin-bottom: 4px;
             border-radius: 8px;
             cursor: pointer;
             transition: all 0.2s ease;
             border-left: 3px solid transparent;
         }

         .conversation-item:hover {
             background: #e8e8e8;
             border-left-color: #007AFF;
         }

         .conversation-item.active {
             background: #e3f2fd;
             border-left-color: #007AFF;
         }

         .conversation-title {
             font-size: 14px;
             color: #333;
             overflow: hidden;
             text-overflow: ellipsis;
             white-space: nowrap;
         }

         .conversation-time {
             font-size: 11px;
             color: #999;
         }

         .qa-content {
             flex: 1;
             font-size: 13px;
             line-height: 1.4;
             white-space: pre-wrap;
         }

         /* 思考过程样式 */
         .thinking-process {
             background: #f0f9ff;
             border: 1px solid #e1f5fe;
             border-radius: 12px;
             padding: 12px 16px;
             margin: 8px 0;
             font-size: 13px;
             color: #666;
             position: relative;
             animation: thinking 1.5s ease-in-out infinite;
         }

         .thinking-process::before {
             content: '🤔';
             margin-right: 8px;
         }

         @keyframes thinking {
             0%, 100% { opacity: 0.7; }
             50% { opacity: 1; }
         }

         .thinking-dots::after {
             content: '';
             display: inline-block;
             animation: dots 1.5s steps(4, end) infinite;
         }

         @keyframes dots {
             0%, 20% { content: ''; }
             40% { content: '.'; }
             60% { content: '..'; }
             80%, 100% { content: '...'; }
         }

         /* 主对话区域样式 */
         .main-chat-area {
             background: white;
             border-radius: 16px;
             box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
             border: 1px solid #e4e7ed;
             margin-bottom: 20px;
             height: 600px;
             display: flex;
             flex-direction: column;
         }

         .main-chat-header {
             padding: 16px 20px;
             border-bottom: 1px solid #f0f0f0;
             display: flex;
             justify-content: space-between;
             align-items: center;
             background: #fafbfc;
             border-radius: 16px 16px 0 0;
         }

         .main-chat-header h3 {
             margin: 0;
             font-size: 16px;
             color: #303133;
             font-weight: 600;
         }

         .main-chat-content {
             flex: 1;
             display: flex;
             flex-direction: column;
             overflow: hidden;
         }

         .main-chat-messages {
             flex: 1;
             padding: 20px;
             overflow-y: auto;
             display: flex;
             flex-direction: column;
             gap: 16px;
         }

         .main-chat-item {
             display: flex;
             flex-direction: column;
             gap: 12px;
         }

         .main-chat-question,
         .main-chat-answer {
             display: flex;
             align-items: flex-start;
             gap: 12px;
             max-width: 70%;
         }

         .main-chat-question {
             align-self: flex-end;
             flex-direction: row-reverse;
         }

         .main-chat-answer {
             align-self: flex-start;
         }

         .main-chat-bubble {
             border-radius: 18px;
             padding: 12px 16px;
             position: relative;
             max-width: 100%;
             word-wrap: break-word;
         }

         .user-bubble {
             background: #007AFF;
             color: white;
             border-bottom-right-radius: 6px;
         }

         .bot-bubble {
             background: #f0f0f0;
             color: #333;
             border-bottom-left-radius: 6px;
         }

         .main-chat-avatar {
             flex-shrink: 0;
             width: 32px;
             height: 32px;
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             font-size: 16px;
             font-weight: 600;
         }

         .user-avatar {
             background: #007AFF;
             color: white;
         }

         .bot-avatar {
             background: #34C759;
             color: white;
         }

         .main-chat-text {
             font-size: 14px;
             line-height: 1.5;
             white-space: pre-wrap;
         }
         
         @media (max-width: 768px) {
             .qa-actions {
                 flex-direction: column;
                 align-items: stretch;
             }
             
             .qa-suggestions {
                 margin-bottom: 15px;
             }
             
             .qa-buttons {
                 justify-content: center;
             }
         }
    </style>
</head>
<body>
    <div id="app">
        <!-- 页面头部 -->
        <div class="header">
            <h1>专卖案件文书大模型智办系统</h1>
            <p>智能化案件处理 · 高效文书生成 · 专业案件分析</p>
        </div>
        <!-- 左侧边栏 -->
        <div class="left-sidebar" :class="{ collapsed: sidebarCollapsed }">
            <!-- 问答历史记录 -->
            <div class="qa-history">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    <i>◀</i>
                </button>
                <div class="qa-history-content">
                    <div class="qa-history-header">
                        <h4>对话</h4>
                        <el-button size="small" type="text" @click="clearHistory">
                            <el-icon><Delete /></el-icon>
                        </el-button>
                    </div>
                    
                    <div class="qa-history-list" v-if="conversationHistory.length > 0">
                        <div class="conversation-item"
                             v-for="(conversation, index) in conversationHistory.slice(0, 8)"
                             :key="conversation.id"
                             :class="{ active: conversation.id === currentConversationId }"
                             @click="openConversation(conversation)">
                            <div class="conversation-title">{{ conversation.title }}</div>
                        </div>
                    </div>
                    <div v-else class="no-history">
                        <p>暂无对话历史</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧边栏 -->
        <div class="right-sidebar" :class="{ collapsed: rightSidebarCollapsed }">
            <button class="right-sidebar-toggle" @click="toggleRightSidebar">
                <i>▶</i>
            </button>
            <div class="recent-activity-content">
                <div class="recent-activity-header">
                    <h4>
                        <el-icon><Clock /></el-icon>
                        最近活动
                    </h4>
                </div>
                <div class="recent-activity-list">
                    <el-timeline>
                        <el-timeline-item
                            v-for="activity in recentActivities"
                            :key="activity.id"
                            :timestamp="activity.time"
                            :type="activity.type"
                            size="small">
                            {{ activity.content }}
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 主内容区域 -->
            <div class="main-content" :class="{ 'right-sidebar-collapsed': rightSidebarCollapsed }">
            <!-- 主对话区域 -->
            <div class="main-chat-area" v-if="showMainChat">
                <div class="main-chat-header">
                    <h3>智能助手对话</h3>
                    <el-button size="small" @click="showMainChat = false">
                        <el-icon><Close /></el-icon>
                    </el-button>
                </div>
                <div class="main-chat-content">
                    <div class="main-chat-messages">
                        <div class="main-chat-item" v-for="(item, index) in mainChatHistory" :key="index">
                            <div class="main-chat-question">
                                <div class="main-chat-bubble user-bubble">
                                    <div class="main-chat-text">{{ item.question }}</div>
                                </div>
                                <div class="main-chat-avatar user-avatar">
                                    <el-icon><User /></el-icon>
                                </div>
                            </div>
                            <div class="main-chat-answer" v-if="item.answer">
                                <div class="main-chat-avatar bot-avatar">
                                    <el-icon><Robot /></el-icon>
                                </div>
                                <div class="main-chat-bubble bot-bubble">
                                    <div class="main-chat-text" v-html="item.answer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="main-features-grid" v-else>
                <!-- 系统功能 - 单独一行 -->
                <div class="system-functions-row">
                    <div class="feature-section system-functions">
                        <div class="regulation-header-row">
                            <span class="title-span">🚀 系统功能</span>
                        </div>
                        <div class="function-cards-container">
                            <!-- 案卷查询 -->
                            <div class="function-card query-card" @click="navigateToFunction('query')">
                                <div class="function-icon query-icon">
                                    <el-icon><Folder /></el-icon>
                                </div>
                                <div class="function-title">案卷查询</div>
                                <div class="function-description">
                                    查询已生成的案卷档案，支持编辑和导出
                                </div>
                            </div>
                            <!-- 案卷生成 -->
                            <div class="function-card generation-card" @click="navigateToFunction('generation')">
                                <div class="function-icon generation-icon">
                                    <el-icon><Document /></el-icon>
                                </div>
                                <div class="function-title">案卷生成</div>
                                <div class="function-description">
                                    智能生成案件文书，自动填充案件信息
                                </div>
                            </div>
                            <!-- 案卷评查 -->
                            <div class="function-card review-card" @click="navigateToFunction('review')">
                                <div class="function-icon review-icon">
                                    <el-icon><Check /></el-icon>
                                </div>
                                <div class="function-title">案卷评查</div>
                                <div class="function-description">
                                    智能审核案件材料，检查文书规范性
                                </div>
                            </div>
                        <!-- 智能对话 -->
                        <div class="function-card chat-card" @click="navigateToFunction('analysis')">
                            <div class="function-icon chat-icon">
                                <span style="font-size: 20px;">💬</span>
                            </div>
                            <div class="function-title">多维分析</div>
                            <div class="function-description">
                                与AI助手进行专业对话咨询
                            </div>
                        </div>
                        </div>
                    </div>
                </div>

                <!-- 系统概览和法规更新公告 - 同一行 -->
                <div class="bottom-sections-row">
                    <!-- 系统概览 -->
                    <div class="feature-section stats-overview compact">
                        <div class="regulation-header-row">
                            <span class="title-span">📊 系统概览</span>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">{{ stats.totalCases }}</div>
                                <div class="stat-label">累计处理案件</div>
                                <el-progress :percentage="75" :show-text="false" :stroke-width="4" color="#409EFF"></el-progress>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ stats.todayCases }}</div>
                                <div class="stat-label">今日新增案件</div>
                                <el-progress :percentage="60" :show-text="false" :stroke-width="4" color="#67C23A"></el-progress>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ stats.pendingReview }}</div>
                                <div class="stat-label">待评查案件</div>
                                <el-progress :percentage="30" :show-text="false" :stroke-width="4" color="#E6A23C"></el-progress>
                            </div>
                        </div>
                    </div>

                    <!-- 法规更新公告 -->
                    <div class="feature-section regulation-updates compact">
                        <div class="regulation-header-row">
                            <span class="title-span">📄 法规更新公告</span>
                            <a href="#" class="view-more-link" @click="navigateToRegulations">
                                查看更多法规
                                <el-icon><ArrowRight /></el-icon>
                            </a>
                        </div>
                        <div class="regulation-items">
                            <div class="regulation-item" v-for="(regulation, index) in regulations.slice(0, 3)" :key="index">
                                <div class="regulation-header">
                                    <el-tag :type="regulation.type" size="small">
                                        <el-icon><Document /></el-icon>
                                        {{ regulation.category }}
                                    </el-tag>
                                     <div class="regulation-title" :data-title="regulation.title">{{ regulation.title }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                

            </div>

            <!-- 问答输入框区域 -->
            <div class="qa-section">
                <div class="qa-examples-section">
                    <div class="qa-examples-container">
                        <div class="qa-examples-list">
                            <div class="qa-example-item">
                                <div class="qa-example-question">
                                    <div class="qa-bubble">
                                        <div class="qa-content">如何快速生成案卷？</div>
                                    </div>
                                    <div class="qa-icon user-icon">
                                        <el-icon><User /></el-icon>
                                    </div>
                                </div>
                                <div class="qa-example-answer">
                                    <div class="qa-icon bot-icon">
                                        <el-icon><Robot /></el-icon>
                                    </div>
                                    <div class="qa-bubble">
                                        <div class="qa-content">
                                            <div class="thinking-process">正在思考您的问题<span class="thinking-dots"></span></div>
                                            智能生成案卷可点击<a href="case-generation.html" style="color: #409EFF; text-decoration: none; font-weight: 600;">此处</a>，系统支持单独生成特定文书和批量生成所有案卷文件两种模式。只需填写基本案件信息，AI将自动生成规范的案卷文书。
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 保留原有的其他模拟案例 -->
                        </div>
                    </div>
                </div>
                
                <div class="qa-input-container">
                    <div class="qa-input-wrapper">
                        <el-input
                            v-model="questionInput"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入您的问题，例如：如何处理无证经营案件？行政处罚的程序是什么？"
                            class="qa-input"
                            @keydown.ctrl.enter="sendQuestion"
                            resize="none">
                        </el-input>
                        
                        <!-- 已上传文件显示区域 -->
                        <div class="uploaded-files" v-if="uploadedFiles.length > 0">
                            <div class="file-item" v-for="(file, index) in uploadedFiles" :key="index">
                                <el-icon class="file-icon">
                                    <Document v-if="file.type === 'file'"></Document>
                                    <Picture v-else></Picture>
                                </el-icon>
                                <span class="file-name">{{ file.name }}</span>
                                <el-button size="small" type="text" @click="removeFile(index)" class="remove-btn">
                                    <el-icon><Close /></el-icon>
                                </el-button>
                            </div>
                        </div>                        
                        <!-- 按钮区域 -->
                        <div class="qa-actions">
                            <div class="qa-buttons">
                                <!-- 上传按钮 -->
                                <el-upload
                                    class="upload-demo"
                                    action="#"
                                    :auto-upload="false"
                                    :show-file-list="false"
                                    accept="image/*"
                                    @change="handleImageUpload">
                                    <el-button size="small">
                                       <el-icon><PictureFilled /></el-icon>
                                        图片
                                    </el-button>
                                </el-upload>
                                
                                <el-upload
                                    class="upload-demo"
                                    action="#"
                                    :auto-upload="false"
                                    :show-file-list="false"
                                    accept=".pdf,.doc,.docx,.txt,.xls,.xlsx"
                                    @change="handleFileUpload">
                                    <el-button size="small">
                                        <el-icon><Paperclip /></el-icon>
                                        附件
                                    </el-button>
                                </el-upload>
                                
                                <!-- 操作按钮 -->
                                <el-button @click="clearInput" size="small">
                                    <el-icon><Delete /></el-icon>
                                    清空
                                </el-button>
                                <el-button type="primary" @click="sendQuestion" size="small" :loading="isAnswering">
                                   <el-icon><Top /></el-icon>
                                    {{ isAnswering ? '思考中...' : '发送问题' }}
                                </el-button>
                            </div>
                        </div>
                    </div>
            </div>
            <!-- 主内容区域结束 -->
        </div>
        <!-- 主容器结束 -->
    </div>
    </div>

<script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        const app = createApp({
            data() {
                return {
                    sidebarCollapsed: false,
                    rightSidebarCollapsed: false,
                    uploadedFiles: [],
                    showMainChat: false,
                    mainChatHistory: [],
                    conversationHistory: [],
                    currentConversationId: null,
                    stats: {
                        totalCases: 1248,
                        todayCases: 23,
                        pendingReview: 15,
                        efficiency: 85
                    },
                    // 滚动公告数据
                    announcements: [
                        { content: '关于印发《烟草专卖行政处罚程序规定》的通知（国烟法[2023]第125号）' },
                        { content: '关于加强烟草专卖行政处罚案件办理工作的通知（国烟法[2023]第98号）' },
                        { content: '关于进一步规范烟草专卖行政处罚自由裁量权的指导意见（国烟法[2023]第76号）' },
                        { content: '关于印发《烟草专卖许可证管理办法实施细则》的通知（国烟法[2022]第203号）' }
                    ],
                    // 法规更新公告数据
                    // 法规更新公告数据 - 首页只显示最新的10条
                    regulations: [
                        {
                            title: '关于印发《烟草专卖行政处罚程序规定》的通知',
                            category: '行政处罚',
                            date: '2023-12-15',
                            type: 'primary'
                        },
                        {
                            title: '关于加强烟草专卖行政处罚案件办理工作的通知',
                            category: '案件办理',
                            date: '2023-11-20',
                            type: 'success'
                        },
                        {
                            title: '关于进一步规范烟草专卖行政处罚自由裁量权的指导意见',
                            category: '行政处罚',
                            date: '2023-10-08',
                            type: 'warning'
                        },
                        {
                            title: '关于印发《烟草专卖许可证管理办法实施细则》的通知',
                            category: '许可管理',
                            date: '2022-12-25',
                            type: 'info'
                        },
                        {
                            title: '关于进一步加强烟草专卖品市场监管工作的通知',
                            category: '市场监管',
                            date: '2022-11-15',
                            type: 'success'
                        },
                        {
                            title: '关于印发《烟草专卖行政处罚证据规定》的通知',
                            category: '行政处罚',
                            date: '2022-09-30',
                            type: 'primary'
                        },
                        {
                            title: '关于加强烟草专卖行政执法与刑事司法衔接工作的实施意见',
                            category: '执法衔接',
                            date: '2022-08-12',
                            type: 'danger'
                        },
                        {
                            title: '关于印发《烟草专卖行政处罚听证程序规定》的通知',
                            category: '行政处罚',
                            date: '2022-07-20',
                            type: 'warning'
                        },
                        {
                            title: '关于进一步规范烟草专卖行政执法自由裁量权的指导意见',
                            category: '行政执法',
                            date: '2022-06-15',
                            type: 'info'
                        },
                        {
                            title: '关于印发《烟草专卖行政处罚案卷评查办法》的通知',
                            category: '案卷评查',
                            date: '2022-05-10',
                            type: 'success'
                        }
                    ],
                    recentActivities: [
                        {
                            id: 1,
                            content: '惠阳烟处（2025）第5号案件文书生成完成',
                            time: '2025-01-04 15:30',
                            type: 'success'
                        },
                        {
                            id: 2,
                            content: '徐某案件通过了智能评查',
                            time: '2025-01-04 14:45',
                            type: 'primary'
                        },
                        {
                            id: 3,
                            content: '完成1325条卷烟扣押清单生成',
                            time: '2025-01-04 13:20',
                            type: 'warning'
                        },
                        {
                            id: 4,
                            content: '惠州市惠阳区案件数据同步完成',
                            time: '2025-01-04 10:00',
                            type: 'info'
                        }
                    ],

                    // 问答相关数据
                    questionInput: '',
                    isAnswering: false,
                    quickQuestions: [
                        '如何处理无证经营案件？',
                        '如何快速生成案卷？',
                        '行政处罚的程序是什么？',
                        '案件文书模板在哪里？',
                        '如何计算罚款金额？',
                        '证据收集要注意什么？'
                    ],
                    qaHistory: [],
                    // 初始化对话历史记录
                    conversationHistory: [
                        {
                            id: 1,
                            title: '查询1-5月双喜相关案件，并将结果填充到文件中',
                            time: '今天 14:30',
                            messages: []
                        },
                        {
                            id: 2,
                            title: '帮我分析一下各区域的案件总数情况',
                            time: '今天 13:45',
                            messages: []
                        },
                        {
                            id: 3,
                            title: '今天是什么节日',
                            time: '今天 10:20',
                            messages: []
                        },
                        {
                            id: 4,
                            title: '你能做什么',
                            time: '昨天 16:15',
                            messages: []
                        },
                        {
                            id: 5,
                            title: '构建注册条文知识库开发结合...',
                            time: '昨天 14:30',
                            messages: []
                        },
                        {
                            id: 6,
                            title: '为何nacos-client-2.0.2无...',
                            time: '昨天 11:20',
                            messages: []
                        },
                        {
                            id: 7,
                            title: 'nacos-client-2.0.2无法连...',
                            time: '2天前',
                            messages: []
                        }
                    ]
                };
            },
            methods: {
                toggleSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                },

                // 切换右侧边栏
                toggleRightSidebar() {
                    this.rightSidebarCollapsed = !this.rightSidebarCollapsed;
                },
                navigateToFunction(type) {
                    const messages = {
                        generation: '正在进入案卷生成模块...',
                        review: '正在进入案卷评查模块...',
                        analysis: '正在进入案件多维分析模块...',
                        query: '正在进入案卷查询模块...'
                    };
                    
                    ElMessage({
                        message: messages[type],
                        type: 'success',
                        duration: 2000
                    });
                    
                    // 页面跳转逻辑
                    setTimeout(() => {
                        if (type === 'generation') {
                            window.location.href = 'case-generation.html';
                        } else if (type === 'review') {
                            window.location.href = 'case-review.html';
                        } else if (type === 'analysis') {
                            window.location.href = 'case-analysis.html';
                        } else if (type === 'query') {
                            window.location.href = 'case-query.html';
                        }
                    }, 1000);
                },
                
                // 跳转到法规更新公告页面
                navigateToRegulations() {
                    ElMessage({
                        message: '正在进入法规更新公告页面...',
                        type: 'info',
                        duration: 2000
                    });
                    
                    setTimeout(() => {
                        window.location.href = 'regulation-list.html';
                    }, 1000);
                },
                
                // 文件上传处理方法
                handleImageUpload(file) {
                    const fileObj = {
                        name: file.name,
                        type: 'image',
                        file: file.raw
                    };
                    this.uploadedFiles.push(fileObj);
                    ElMessage({
                        message: `图片 "${file.name}" 上传成功`,
                        type: 'success',
                        duration: 2000
                    });
                },
                
                handleFileUpload(file) {
                    const fileObj = {
                        name: file.name,
                        type: 'file',
                        file: file.raw
                    };
                    this.uploadedFiles.push(fileObj);
                    ElMessage({
                        message: `附件 "${file.name}" 上传成功`,
                        type: 'success',
                        duration: 2000
                    });
                },
                
                removeFile(index) {
                    const fileName = this.uploadedFiles[index].name;
                    this.uploadedFiles.splice(index, 1);
                    ElMessage({
                        message: `已删除文件 "${fileName}"`,
                        type: 'info',
                        duration: 2000
                    });
                },
                
                // 发送问题
                sendQuestion() {
                    if (!this.questionInput.trim()) {
                        ElMessage({
                            message: '请输入您的问题',
                            type: 'warning',
                            duration: 2000
                        });
                        return;
                    }

                    const question = this.questionInput.trim();
                    this.isAnswering = true;

                    // 显示主对话区域
                    this.showMainChat = true;

                    // 创建新的对话记录或添加到当前对话
                    if (!this.currentConversationId) {
                        // 创建新对话
                        const newConversation = {
                            id: Date.now(),
                            title: question.length > 30 ? question.substring(0, 30) + '...' : question,
                            time: this.formatTime(new Date()),
                            messages: []
                        };
                        this.conversationHistory.unshift(newConversation);
                        this.currentConversationId = newConversation.id;
                    }

                    // 先添加用户问题到主对话历史记录
                    const newMainChatItem = {
                        question: question,
                        answer: '',
                        thinking: true
                    };
                    this.mainChatHistory.push(newMainChatItem);

                    // 添加到当前对话的消息记录
                    const currentConversation = this.conversationHistory.find(c => c.id === this.currentConversationId);
                    if (currentConversation) {
                        currentConversation.messages.push(newMainChatItem);
                    }

                    // 显示思考过程
                    setTimeout(() => {
                        const mainIndex = this.mainChatHistory.length - 1;
                        const sideIndex = this.qaHistory.length - 1;
                        const thinkingText = '<div class="thinking-process">正在思考您的问题<span class="thinking-dots"></span></div>';
                        this.mainChatHistory[mainIndex].answer = thinkingText;
                        this.qaHistory[sideIndex].answer = thinkingText;
                    }, 500);

                    // 模拟AI回答
                    setTimeout(() => {
                        const answers = {
                            '如何处理无证经营案件？': '无证经营案件处理流程：1. 现场检查并收集证据；2. 制作现场检查笔录；3. 调取相关证据材料；4. 制作调查询问笔录；5. 案件审理；6. 制作行政处罚决定书；7. 送达处罚决定书；8. 执行处罚决定。',
                            '如何快速生成案卷？': '智能生成案卷可点击<a href="case-generation.html" style="color: #409EFF; text-decoration: none;">此处</a>',
                            '行政处罚的程序是什么？': '行政处罚程序包括：1. 立案；2. 调查取证；3. 告知当事人权利；4. 听取当事人陈述申辩；5. 案件审理；6. 作出处罚决定；7. 送达处罚决定书；8. 执行。对于较大数额罚款，还需要进行听证程序。',
                            '案件文书模板在哪里？': '案件文书模板可以在系统的"案卷生成"模块中找到，包括现场检查笔录、调查询问笔录、行政处罚决定书等标准模板。您也可以在文档窗口中查看最近使用的模板文件。',
                            '如何计算罚款金额？': '罚款金额计算需要根据《烟草专卖法》及相关法规，结合违法行为的性质、情节、危害后果等因素，在法定幅度内进行自由裁量。系统提供自动计算功能，会根据案件具体情况给出建议金额。',
                            '证据收集要注意什么？': '证据收集要注意：1. 证据的合法性、真实性、关联性；2. 现场拍照要清晰完整；3. 笔录制作要规范；4. 物证保全要妥善；5. 证人证言要客观；6. 书证要原件或核实无误的复印件；7. 证据链要完整。'
                        };
                        
                        let answer = answers[question];
                        if (!answer) {
                            // 简单的关键词匹配
                            if (question.includes('无证') || question.includes('经营')) {
                                answer = '无证经营是指未取得烟草专卖零售许可证经营烟草制品零售业务的行为。处理时需要收集充分证据，包括现场检查笔录、涉案物品清单、当事人身份证明等。';
                            } else if (question.includes('罚款') || question.includes('金额')) {
                                answer = '罚款金额的确定需要综合考虑违法行为的性质、情节、社会危害程度等因素，在法定幅度内合理裁量。建议参考相关裁量基准和类似案例。';
                            } else if (question.includes('文书') || question.includes('模板')) {
                                answer = '系统提供完整的案件文书模板库，包括各类笔录、决定书等。您可以通过案卷生成功能选择合适的模板，系统会自动填充相关信息。';
                            } else {
                                answer = '感谢您的提问。这是一个很好的问题，建议您查阅相关法律法规或咨询专业人员。如果是系统操作问题，可以查看帮助文档或联系技术支持。';
                            }
                        }

                        // 更新主对话区域和侧边栏的答案
                        const mainIndex = this.mainChatHistory.length - 1;
                        const sideIndex = this.qaHistory.length - 1;
                        this.mainChatHistory[mainIndex].answer = answer;
                        this.mainChatHistory[mainIndex].thinking = false;
                        this.qaHistory[sideIndex].answer = answer;
                        this.qaHistory[sideIndex].thinking = false;

                        this.questionInput = '';
                        this.isAnswering = false;

                        ElMessage({
                            message: '回答已生成',
                            type: 'success',
                            duration: 2000
                        });
                    }, 3000);
                },
                
                // 选择建议问题
                selectSuggestion(suggestion) {
                    this.questionInput = suggestion;
                },
                
                // 清空输入
                clearInput() {
                    this.questionInput = '';
                },
                
                // 清空历史
                clearHistory() {
                    this.conversationHistory = [];
                    this.mainChatHistory = [];
                    this.currentConversationId = null;
                    ElMessage({
                        message: '对话历史已清空',
                        type: 'info',
                        duration: 2000
                    });
                },

                // 打开对话
                openConversation(conversation) {
                    this.currentConversationId = conversation.id;
                    this.mainChatHistory = [...conversation.messages];
                    this.showMainChat = true;

                    // 更新对话项的激活状态
                    this.conversationHistory.forEach(c => {
                        c.active = c.id === conversation.id;
                    });
                },

                // 格式化时间
                formatTime(date) {
                    const now = new Date();
                    const diff = now - date;
                    const minutes = Math.floor(diff / 60000);
                    const hours = Math.floor(diff / 3600000);
                    const days = Math.floor(diff / 86400000);

                    if (minutes < 1) return '刚刚';
                    if (minutes < 60) return `${minutes}分钟前`;
                    if (hours < 24) return `${hours}小时前`;
                    if (days === 1) return '昨天';
                    if (days < 7) return `${days}天前`;

                    return date.toLocaleDateString();
                },

                // 开始主对话
                startMainChat() {
                    this.showMainChat = true;
                    this.currentConversationId = null;
                    this.mainChatHistory = [];

                    ElMessage({
                        message: '智能对话已开启',
                        type: 'success',
                        duration: 2000
                    });
                }
            },
            mounted() {
                // 模拟实时数据更新
                setInterval(() => {
                    // 随机更新今日案件数
                    if (Math.random() > 0.8) {
                        this.stats.todayCases += Math.floor(Math.random() * 3);
                    }
                }, 30000);
            }
        });
        
        // 注册Element Plus图标
        if (typeof ElementPlusIconsVue !== 'undefined') {
            for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
                app.component(key, component);
            }
            console.log('Element Plus图标注册成功');
        } else {
            console.error('Element Plus图标库未加载，请检查CDN链接');
        }
        
        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
