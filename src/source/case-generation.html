<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案卷生成 - 专卖案件文书大模型智办系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        /* 面包屑样式 */
        .breadcrumb {
            background: #ffffff;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;
            text-decoration: none;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: #409EFF;
        }

        .breadcrumb-item.current {
            color: #303133;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #C0C4CC;
            margin: 0 4px;
        }

        .breadcrumb-icon {
            font-size: 16px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .left-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .right-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            z-index: 2;
        }
        
        .section-title i {
            color: #409EFF;
            font-size: 24px;
        }
        
        .ai-assistant {
            background: linear-gradient(135deg, #E8F4FD 0%, #F0F9FF 100%);
            border: 2px solid #409EFF;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .ai-assistant::before {
            content: '🤖';
            position: absolute;
            top: -5px;
            right: 20px;
            background: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 16px;
            z-index: 1;
        }
        
        .ai-title {
            color: #409EFF;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .document-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .doc-type {
            background: #F8F9FA;
            border: 2px solid #E4E7ED;
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .doc-type:hover {
            border-color: #409EFF;
            background: #E8F4FD;
            transform: translateY(-2px);
        }
        
        .doc-type.active {
            border-color: #409EFF;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
        }
        
        .doc-type i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .doc-type span {
            font-size: 14px;
            font-weight: 500;
        }
        
        .form-section {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
            display: block;
        }
        
        /* 只读字段样式 */
        .el-input.is-disabled .el-input__inner,
        .el-textarea.is-disabled .el-textarea__inner,
        .el-select.is-disabled .el-input__inner {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #606266;
            cursor: not-allowed;
        }
        
        .el-input, .el-textarea, .el-select {
            width: 100%;
        }
        
        .el-textarea .el-textarea__inner {
            min-height: 120px;
        }
        
        .generate-btn {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
        }
        
        .progress-section {
            margin-bottom: 30px;
        }
        
        .progress-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #F0F0F0;
        }
        
        .progress-item:last-child {
            border-bottom: none;
        }
        
        .progress-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .progress-icon.completed {
            background: #67C23A;
        }
        
        .progress-icon.current {
            background: #409EFF;
            animation: pulse 2s infinite;
        }
        
        .progress-icon.pending {
            background: #DCDFE6;
            color: #909399;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .progress-text {
            flex: 1;
        }
        
        .progress-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .progress-desc {
            font-size: 14px;
            color: #606266;
        }
        
        .tips-section {
            background: #FFF9E6;
            border: 1px solid #FADB14;
            border-radius: 8px;
            padding: 15px;
        }
        
        .tips-title {
            color: #E6A23C;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-list {
            list-style: none;
            color: #606266;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .tips-list li {
            margin-bottom: 5px;
            position: relative;
            padding-left: 15px;
        }
        
        .tips-list li::before {
            content: '•';
            color: #E6A23C;
            position: absolute;
            left: 0;
        }
        
        .generation-mode {
            margin-bottom: 20px;
            height: 100px;
            margin-top: 20px;
        }
        
        .generation-mode + .section-title {
            margin-top: 25px;
            margin-bottom: 20px;
        }
        
        .mode-selector {
            display: flex;
            gap: 15px;
            width: 100%;
        }
        
        .mode-option {
            flex: 1;
            margin: 0 !important;
        }
        
        .mode-option .el-radio__input {
            display: none !important;
        }
        
        .mode-option .el-radio__label {
            padding: 0 !important;
            width: 100% !important;
            display: block !important;
            margin-top: 60px;
        }
        
        .mode-content {
            background: #F8F9FA;
            border: 2px solid #E4E7ED;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .mode-option.is-checked .mode-content {
            border-color: #409EFF !important;
            background: linear-gradient(135deg, #E8F4FD 0%, #F0F9FF 100%) !important;
        }
        
        .mode-content i {
            font-size: 28px;
            color: #409EFF;
            margin-bottom: 8px;
            display: block;
        }
        
        .mode-content span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            display: block;
            margin-bottom: 6px;
        }
        
        .mode-content p {
            font-size: 13px;
            color: #606266;
            margin: 0;
            line-height: 1.3;
        }
        
        .batch-preview {
            margin-top: 20px;
            margin-bottom: 30px;
            padding-top: 10px;
        }
        
        .batch-preview .section-title {
            margin-bottom: 15px;
            padding: 10px 0;
            background: transparent;
            z-index: 1;
        }
        
        .batch-files {
            background: #FFFFFF;
            border: 1px solid #E4E7ED;
            border-radius: 8px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        /* 自定义滚动条样式 */
        .batch-files::-webkit-scrollbar {
            width: 6px;
        }
        
        .batch-files::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .batch-files::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            transition: background 0.3s ease;
        }
        
        .batch-files::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .batch-file-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #FFFFFF;
            border: 1px solid #F0F2F5;
            border-radius: 6px;
            margin-bottom: 4px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 40px;
        }
        
        .batch-file-item:last-child {
            margin-bottom: 0;
        }
        
        .batch-file-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }
        
        .file-status {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            flex-shrink: 0;
            background: #f0f5ff;
            border-radius: 50%;
        }
        
        .file-status i {
            font-size: 16px;
            display: block;
            line-height: 1;
        }
        
        /* 不同文件类型的图标颜色 */
        .file-status .el-icon-document { color: #409EFF; }
        .file-status .el-icon-folder-opened { color: #E6A23C; }
        .file-status .el-icon-message { color: #67C23A; }
        .file-status .el-icon-view { color: #909399; }
        .file-status .el-icon-document-checked { color: #409EFF; }
        .file-status .el-icon-chat-line-round { color: #F56C6C; }
        .file-status .el-icon-tickets { color: #E6A23C; }
        .file-status .el-icon-money { color: #67C23A; }
        .file-status .el-icon-bell { color: #F56C6C; }
        .file-status .el-icon-chat-dot-round { color: #909399; }
        .file-status .el-icon-warning { color: #E6A23C; }
        .file-status .el-icon-postcard { color: #409EFF; }
        .file-status .el-icon-box { color: #67C23A; }
        .file-status .el-icon-timer { color: #F56C6C; }
        .file-status .el-icon-finished { color: #409EFF; }
        .file-status .el-icon-circle-check { color: #67C23A; }
        .file-status .el-icon-folder { color: #E6A23C; }
        .file-status .el-icon-cpu { color: #909399; }
        .file-status .el-icon-position { color: #F56C6C; }
        .file-status .el-icon-microphone { color: #409EFF; }
        .file-status .el-icon-user { color: #67C23A; }
        .file-status .el-icon-files { color: #909399; }
        
        .generating-icon {
            color: #409eff !important;
            animation: rotate 2s linear infinite;
        }
        
        .success-icon {
            color: #67c23a !important;
        }
        
        .error-icon {
            color: #f56c6c !important;
        }
        
        /* 生成状态时的背景色 */
        .batch-file-item .file-status:has(.generating-icon) {
            background: #e6f7ff;
        }
        
        .batch-file-item .file-status:has(.success-icon) {
            background: #f6ffed;
        }
        
        .batch-file-item .file-status:has(.error-icon) {
            background: #fff2f0;
        }
        
        .file-info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .file-name {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }
        
        .file-name.clickable {
            color: #409eff;
            text-decoration: underline;
        }
        
        .file-name.clickable:hover {
            color: #66b1ff;
        }
        
        .file-type {
            font-size: 12px;
            color: #6c757d;
            background: #f5f7fa;
            padding: 2px 8px;
            border-radius: 12px;
            white-space: nowrap;
        }
        
        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #409EFF;
            color: #409EFF;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-btn:hover {
            background: #409EFF;
            color: white;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .left-panel, .right-panel {
                padding: 20px;
            }
            
            .document-types {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .mode-selector {
                flex-direction: column;
                gap: 10px;
            }
            
            .mode-content {
                padding: 15px;
                height: 100px;
            }
            
            .mode-content i {
                font-size: 24px;
                margin-bottom: 6px;
            }
            
            .mode-content span {
                font-size: 14px;
                margin-bottom: 4px;
            }
            
            .mode-content p {
                font-size: 12px;
            }
            
            .batch-files {
                max-height: 250px;
            }
            
            .batch-file-item {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <a href="index.html" class="back-btn">
            <i class="el-icon-arrow-left"></i> 返回首页
        </a>
        
        <div class="header">
            <h1>案卷生成</h1>
        </div>
        
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html" class="breadcrumb-item">
                    <i class="breadcrumb-icon">🏠</i>
                    <span>首页</span>
                </a>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item current">
                    <i class="breadcrumb-icon">📝</i>
                    <span>案卷生成</span>
                </span>
            </div>
            
            <div class="main-content">
                <div class="left-panel">
                    <div class="ai-assistant">
                        <div class="ai-title">🎯 AI文案助手</div>
                        <p style="color: #606266; line-height: 1.6;">基于大模型技术，智能生成专业案卷文书，支持多种文档类型，确保内容准确、格式规范。</p>
                    </div>
                    
                    <div class="section-title">
                        <i class="el-icon-setting"></i>
                        生成模式
                    </div>
                    
                    <div class="generation-mode">
                        <el-radio-group v-model="generationMode" class="mode-selector">
                            <el-radio label="single" class="mode-option">
                                <div class="mode-content">
                                    <i class="el-icon-document"></i>
                                    <span>单独生成</span>
                                    <p>选择特定文档类型生成</p>
                                </div>
                            </el-radio>
                            <el-radio label="batch" class="mode-option">
                                <div class="mode-content">
                                    <i class="el-icon-files"></i>
                                    <span>批量生成</span>
                                    <p>一次性生成所有案卷文件</p>
                                </div>
                            </el-radio>
                        </el-radio-group>
                    </div>
                    
                    <div class="section-title" v-if="generationMode === 'single'">
                        <i class="el-icon-document"></i>
                        选择文档类型
                    </div>
                    
                    <div class="document-types" v-if="generationMode === 'single'">
                        <div class="doc-type" :class="{active: selectedDocType === 'inquiry'}" @click="selectedDocType = 'inquiry'">
                            <i class="el-icon-chat-line-round"></i>
                            <span>询问笔录</span>
                        </div>
                        <div class="doc-type" :class="{active: selectedDocType === 'inspection'}" @click="selectedDocType = 'inspection'">
                            <i class="el-icon-view"></i>
                            <span>检查笔录</span>
                        </div>
                        <div class="doc-type" :class="{active: selectedDocType === 'seizure'}" @click="selectedDocType = 'seizure'">
                            <i class="el-icon-lock"></i>
                            <span>扣押清单</span>
                        </div>
                        <div class="doc-type" :class="{active: selectedDocType === 'penalty'}" @click="selectedDocType = 'penalty'">
                            <i class="el-icon-warning"></i>
                            <span>处罚决定</span>
                        </div>
                        <div class="doc-type" :class="{active: selectedDocType === 'notice'}" @click="selectedDocType = 'notice'">
                            <i class="el-icon-bell"></i>
                            <span>告知书</span>
                        </div>
                        <div class="doc-type" :class="{active: selectedDocType === 'hearing'}" @click="selectedDocType = 'hearing'">
                            <i class="el-icon-microphone"></i>
                            <span>听证笔录</span>
                        </div>
                    </div>
                    
                    <div class="batch-preview" v-if="generationMode === 'batch'">
                        <div class="section-title">
                            <i class="el-icon-files"></i>
                            将生成以下文件
                        </div>
                        <div class="batch-files">
                            <div class="batch-file-item" v-for="file in batchFiles" :key="file.name" @click="navigateToFileDetail(file)">
                                <div class="file-status">
                                    <i v-if="file.status === 'pending'" :class="file.icon"></i>
                                    <i v-else-if="file.status === 'generating'" class="el-icon-loading generating-icon"></i>
                                    <i v-else-if="file.status === 'success'" class="el-icon-circle-check success-icon"></i>
                                    <el-tooltip v-else-if="file.status === 'error'" :content="file.error" placement="top">
                                        <i class="el-icon-warning-outline error-icon"></i>
                                    </el-tooltip>
                                </div>
                                <div class="file-info">
                                    <div class="file-name" :class="{ 'clickable': file.status === 'success' }">{{ file.name }}</div>
                                    <div class="file-type">{{ file.type }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="section-title">
                        <i class="el-icon-edit"></i>
                        填写案件信息
                    </div>
                    
                    <div class="form-section">
                        <label class="form-label">案件名称</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <el-select v-model="caseInfo.name" placeholder="请选择或输入案件名称" filterable allow-create @change="onCaseNameChange" style="flex: 1;">
                                <el-option 
                                    v-for="caseItem in predefinedCases" 
                                    :key="caseItem.name" 
                                    :label="caseItem.name" 
                                    :value="caseItem.name">
                                </el-option>
                            </el-select>
                            <el-button v-if="isFormDisabled" @click="resetForm" size="small" type="warning" plain>
                                🔓 重新编辑
                            </el-button>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <label class="form-label">当事人信息</label>
                        <el-input v-model="caseInfo.party" placeholder="请输入当事人姓名、身份证号等信息" :disabled="isFormDisabled"></el-input>
                    </div>
                    
                    <div class="form-section">
                        <label class="form-label">案件类型</label>
                        <el-select v-model="caseInfo.type" placeholder="请选择案件类型" :disabled="isFormDisabled">
                            <el-option label="无证经营" value="unlicensed"></el-option>
                            <el-option label="销售假冒伪劣" value="counterfeit"></el-option>
                            <el-option label="超范围经营" value="overscope"></el-option>
                            <el-option label="其他违法行为" value="other"></el-option>
                        </el-select>
                    </div>
                    
                    <div class="form-section">
                        <label class="form-label">案件描述</label>
                        <el-input type="textarea" v-model="caseInfo.description" placeholder="请详细描述案件经过、违法事实等信息..." :disabled="isFormDisabled"></el-input>
                    </div>
                    
                    <button class="generate-btn" @click="generateDocument" :disabled="generating">
                        <span v-if="!generating">
                            <span v-if="generationMode === 'single'">🚀 智能生成文书</span>
                            <span v-else>📦 批量生成所有文件</span>
                        </span>
                        <span v-else>
                            <span v-if="generationMode === 'single'">⏳ 正在生成中...</span>
                            <span v-else>⏳ 正在批量生成中...</span>
                        </span>
                    </button>
                </div>
                
                <div class="right-panel">
                    <div class="section-title">
                        <i class="el-icon-loading"></i>
                        生成进度
                    </div>
                    
                    <div class="progress-section">
                        <div class="progress-item">
                            <div class="progress-icon" :class="getProgressStatus(0)">
                                <i class="el-icon-edit"></i>
                            </div>
                            <div class="progress-text">
                                <div class="progress-title">信息收集</div>
                                <div class="progress-desc">分析案件信息和文档类型</div>
                            </div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-icon" :class="getProgressStatus(1)">
                                <i class="el-icon-cpu"></i>
                            </div>
                            <div class="progress-text">
                                <div class="progress-title">AI智能分析</div>
                                <div class="progress-desc">大模型理解案件要素</div>
                            </div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-icon" :class="getProgressStatus(2)">
                                <i class="el-icon-document-copy"></i>
                            </div>
                            <div class="progress-text">
                                <div class="progress-title">文书生成</div>
                                <div class="progress-desc">按照法律模板生成文书</div>
                            </div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-icon" :class="getProgressStatus(3)">
                                <i class="el-icon-check"></i>
                            </div>
                            <div class="progress-text">
                                <div class="progress-title">质量检查</div>
                                <div class="progress-desc">校验格式和内容完整性</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tips-section">
                        <div class="tips-title">
                            <i class="el-icon-warning"></i>
                            使用提示
                        </div>
                        <ul class="tips-list">
                            <li>请确保案件信息填写完整准确</li>
                            <li>选择正确的文档类型以获得最佳效果</li>
                            <li>生成后可进一步编辑和完善内容</li>
                            <li>系统会自动保存生成记录</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    generationMode: 'single',
                    selectedDocType: '',
                    generating: false,
                    currentStep: 1,
                    isFormDisabled: false,
                    caseInfo: {
                        name: '',
                        party: '',
                        type: '',
                        description: ''
                    },

                    predefinedCases: [
                        {
                            name: '徐某未在当地烟草专卖批发企业进货案',
                            party: '徐伟雄，男，汉族，身份证号：4452***********19',
                            type: 'unlicensed',
                            description: '2025年1月4日10时30分，我局执法人员黄国强、董玉凤出示证件，对惠州市惠阳区淡水某某商贸行进行检查，发现该场所有涉嫌未在当地烟草专卖批发企业进货的卷烟。现场查获黄山（新一代）100条、黄金叶（蓝）100条、黄山（硬）75条、红河（硬）100条、红双喜（硬经典）525条等9个品牌规格共计1325条。'
                        },
                        {
                            name: '某商店销售假冒卷烟案',
                            party: '李某，女，汉族，1978年3月出生，身份证号：4401***********XX',
                            type: 'counterfeit',
                            description: '2024年12月20日，根据群众举报，执法人员在惠州市惠阳区某商店检查发现李某销售假冒"中华"、"玉溪"等品牌卷烟。经鉴定，查获的卷烟均为假冒伪劣产品，共计8条。'
                        },
                        {
                            name: '某便利店超范围经营烟草专卖品案',
                            party: '王某，男，汉族，1990年11月出生，身份证号：4401***********XX',
                            type: 'overscope',
                            description: '2024年11月5日，执法人员在例行检查中发现王某在其便利店内销售非烟草专卖许可证载明的卷烟品种。经查，王某销售的部分进口卷烟未在其烟草专卖零售许可证核准的经营范围内。'
                        },
                        {
                            name: '某网店网络销售卷烟案',
                            party: '赵某，男，汉族，1995年9月出生，身份证号：4401***********XX',
                            type: 'other',
                            description: '2024年10月10日，执法人员通过网络监测发现赵某在某电商平台上开设网店销售卷烟。经查，赵某未取得烟草专卖零售许可证，通过互联网销售卷烟共计约20条，涉案金额约5000元。'
                        }
                    ],
                    batchFiles: [
                        { name: '举报记录表', type: '记录类', icon: 'el-icon-document', status: 'pending', error: null },
                        { name: '立案（不予立案）报告表', type: '报告类', icon: 'el-icon-folder-opened', status: 'pending', error: null },
                        { name: '指定管辖通知书', type: '通知类', icon: 'el-icon-message', status: 'pending', error: null },
                        { name: '检查（勘验）笔录', type: '笔录类', icon: 'el-icon-view', status: 'pending', error: null },
                        { name: '先行登记保存批准书或通知书', type: '批准类', icon: 'el-icon-document-checked', status: 'pending', error: null },
                        { name: '询问笔录', type: '笔录类', icon: 'el-icon-chat-line-round', status: 'pending', error: null },
                        { name: '抽样取证物品清单', type: '清单类', icon: 'el-icon-tickets', status: 'pending', error: null },
                        { name: '涉案烟草专卖品核价表', type: '核价类', icon: 'el-icon-money', status: 'pending', error: null },
                        { name: '调查终结报告', type: '报告类', icon: 'el-icon-document', status: 'pending', error: null },
                        { name: '行政处罚事先告知书', type: '告知类', icon: 'el-icon-bell', status: 'pending', error: null },
                        { name: '案件集体讨论记录', type: '记录类', icon: 'el-icon-chat-dot-round', status: 'pending', error: null },
                        { name: '行政处罚决定书', type: '决定类', icon: 'el-icon-warning', status: 'pending', error: null },
                        { name: '送达回证', type: '证明类', icon: 'el-icon-postcard', status: 'pending', error: null },
                        { name: '罚没物品移交单', type: '移交类', icon: 'el-icon-box', status: 'pending', error: null },
                        { name: '延期/分期缴纳罚款通知书', type: '通知类', icon: 'el-icon-timer', status: 'pending', error: null },
                        { name: '案件调查终结报告', type: '报告类', icon: 'el-icon-finished', status: 'pending', error: null },
                        { name: '结案报告表', type: '报告类', icon: 'el-icon-circle-check', status: 'pending', error: null },
                        { name: '卷宗封面与目录', type: '目录类', icon: 'el-icon-folder', status: 'pending', error: null },
                        { name: '电子数据提取笔录', type: '笔录类', icon: 'el-icon-cpu', status: 'pending', error: null },
                        { name: '案件移送函', type: '函件类', icon: 'el-icon-position', status: 'pending', error: null },
                        { name: '听证相关文书', type: '听证类', icon: 'el-icon-microphone', status: 'pending', error: null },
                        { name: '当事人身份证复印件', type: '证件类', icon: 'el-icon-user', status: 'pending', error: null }
                    ]
                };
            },
            mounted() {
                // 检查是否有从案件查询页面传递的案件信息
                const selectedCase = localStorage.getItem('selectedCase');
                if (selectedCase) {
                    try {
                        const caseData = JSON.parse(selectedCase);
                        // 自动填充案件信息
                        this.caseInfo.name = caseData.caseNumber || '';
                        this.caseInfo.party = caseData.partyName || '';
                        this.caseInfo.type = caseData.caseType || '';
                        this.caseInfo.description = caseData.description || `案件编号：${caseData.caseNumber}\n当事人：${caseData.partyName}\n办案人员：${caseData.officer}\n创建时间：${caseData.createTime}`;
                        
                        // 设置表单为可编辑状态
                        this.isFormDisabled = false;
                        
                        ElMessage.success('已自动加载案件信息，请确认后生成文书');
                        
                        // 清除localStorage中的案件信息，避免重复使用
                        localStorage.removeItem('selectedCase');
                    } catch (error) {
                        console.error('解析案件信息失败:', error);
                        ElMessage.warning('案件信息格式错误，请手动填写');
                    }
                }
            },
            methods: {
                generateDocument() {
                    if (!this.validateForm()) {
                        return;
                    }
                    
                    this.generating = true;
                    this.currentStep = 0;
                    
                    if (this.generationMode === 'batch') {
                        // 重置所有文件状态
                        this.batchFiles.forEach(file => {
                            file.status = 'pending';
                            file.error = null;
                        });
                        
                        // 启动批量生成模拟
                        this.simulateBatchGeneration();
                    }
                    
                    // 模拟生成过程
                    const steps = [1000, 2000, 1500, 1000];
                    let totalTime = 0;
                    
                    steps.forEach((time, index) => {
                        totalTime += time;
                        setTimeout(() => {
                            this.currentStep = index + 1;
                            if (index === steps.length - 1) {
                                this.generating = false;
                                if (this.generationMode === 'single') {
                                    ElMessage.success('文书生成完成！');
                                } else {
                                    const successCount = this.batchFiles.filter(file => file.status === 'success').length;
                                    ElMessage.success(`批量生成完成！共生成${successCount}个文件`);
                                }
                                // 这里可以跳转到预览页面或下载文件
                            }
                        }, totalTime);
                    });
                },
                
                validateForm() {
                    if (!this.caseInfo.name.trim()) {
                        ElMessage.warning('请输入案件名称');
                        return false;
                    }
                    if (!this.caseInfo.party.trim()) {
                        ElMessage.warning('请输入当事人信息');
                        return false;
                    }
                    if (!this.caseInfo.type) {
                        ElMessage.warning('请选择案件类型');
                        return false;
                    }
                    if (!this.caseInfo.description.trim()) {
                        ElMessage.warning('请输入案件描述');
                        return false;
                    }
                    if (this.generationMode === 'single' && !this.selectedDocType) {
                        ElMessage.warning('请选择文档类型');
                        return false;
                    }
                    return true;
                },
                
                onCaseNameChange(selectedName) {
                    // 查找选中的预定义案件
                    const selectedCase = this.predefinedCases.find(caseItem => caseItem.name === selectedName);
                    
                    if (selectedCase) {
                        // 如果选择的是预定义案件，自动填写其他信息并设为不可编辑
                        this.caseInfo.party = selectedCase.party;
                        this.caseInfo.type = selectedCase.type;
                        this.caseInfo.description = selectedCase.description;
                        this.isFormDisabled = true;
                        
                        ElMessage.success('已自动填写案件信息，其他字段已锁定');
                    } else {
                        // 如果是自定义输入，清空其他字段并设为可编辑
                        this.caseInfo.party = '';
                        this.caseInfo.type = '';
                        this.caseInfo.description = '';
                        this.isFormDisabled = false;
                        
                        if (selectedName && selectedName.trim()) {
                            ElMessage.info('请手动填写案件信息');
                        }
                    }
                },
                
                resetForm() {
                    // 重置表单状态，允许重新编辑
                    this.caseInfo.name = '';
                    this.caseInfo.party = '';
                    this.caseInfo.type = '';
                    this.caseInfo.description = '';
                    this.isFormDisabled = false;
                    
                    ElMessage.info('已重置表单，可重新选择或编辑案件信息');
                },
                
                navigateToFileDetail(file) {
                        if (file.status === 'success') {
                            // 跳转到文件详情页
                            ElMessage.info(`正在打开 ${file.name} 详情页...`);
                            setTimeout(() => {
                                window.location.href = `case-detail.html?name=${encodeURIComponent(file.name)}`;
                            }, 500);
                        }
                    },
                
                simulateBatchGeneration() {
                    // 模拟批量生成过程
                    const files = [...this.batchFiles];
                    let currentIndex = 0;
                    
                    const generateNext = () => {
                        if (currentIndex >= files.length) {
                            return;
                        }
                        
                        const file = files[currentIndex];
                        file.status = 'generating';
                        
                        // 模拟生成时间（1-3秒）
                        const generateTime = Math.random() * 2000 + 1000;
                        
                        setTimeout(() => {
                            // 90%成功率，10%失败率
                            if (Math.random() < 0.9) {
                                file.status = 'success';
                            } else {
                                file.status = 'error';
                                file.error = `生成${file.name}时发生错误：网络连接超时，请重试`;
                            }
                            
                            currentIndex++;
                            generateNext();
                        }, generateTime);
                    };
                    
                    generateNext();
                },
                
                getProgressStatus(step) {
                    if (step < this.currentStep) {
                        return 'completed';
                    } else if (step === this.currentStep && this.generating) {
                        return 'current';
                    } else {
                        return 'pending';
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>