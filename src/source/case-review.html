<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案卷评查 - 专卖案件文书大模型智办系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(230, 162, 60, 0.3);
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        /* 面包屑样式 */
        .breadcrumb {
            background: #ffffff;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;
            text-decoration: none;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: #E6A23C;
        }

        .breadcrumb-item.current {
            color: #303133;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #C0C4CC;
            margin: 0 4px;
        }

        .breadcrumb-icon {
            font-size: 16px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .left-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .right-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            z-index: 2;
        }
        
        .section-title i {
            color: #E6A23C;
            font-size: 24px;
        }
        
        .ai-assistant {
            background: linear-gradient(135deg, #FDF6EC 0%, #FEF7E6 100%);
            border: 2px solid #E6A23C;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }
        
        .ai-assistant::before {
            content: '🔍';
            position: absolute;
            top: -5px;
            right: 20px;
            background: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 16px;
            z-index: 2;
        }
        
        .ai-title {
            color: #E6A23C;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .review-mode {
            margin-bottom: 20px;
            height: 100px;
            margin-top: 20px;
        }
        
        .mode-selector {
            display: flex;
            gap: 15px;
            width: 100%;
        }
        
        .mode-option {
            flex: 1;
            margin: 0 !important;
        }
        
        .mode-option .el-radio__input {
            display: none !important;
        }
        
        .mode-option .el-radio__label {
            padding: 0 !important;
            width: 100% !important;
            display: block !important;
            margin-top: 60px;
        }
        
        .mode-content {
            background: #F8F9FA;
            border: 2px solid #E4E7ED;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .mode-option.is-checked .mode-content {
            border-color: #E6A23C !important;
            background: linear-gradient(135deg, #FDF6EC 0%, #FEF7E6 100%) !important;
        }
        
        .mode-content i {
            font-size: 28px;
            color: #E6A23C;
            margin-bottom: 8px;
            display: block;
        }
        
        .mode-content span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            display: block;
            margin-bottom: 6px;
        }
        
        .mode-content p {
            font-size: 12px;
            color: #909399;
            line-height: 1.4;
        }
        
        /* 案件信息表单样式 */
        .case-info {
            margin-bottom: 30px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-item.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
        }
        
        .case-name-row {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }
        
        .case-name-row .el-select {
            flex: 1;
        }
        
        .re-edit-btn {
            margin-bottom: 2px;
        }
        
        /* 评查流程动画样式 */
        .review-process {
            margin-top: 30px;
        }
        
        .process-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .process-step {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e4e7ed;
            position: relative;
        }
        
        .process-step:last-child {
            border-bottom: none;
        }
        
        /* 步骤图标样式优化 */
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .step-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .step-icon.pending {
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
            color: #909399;
        }
        
        .step-icon.active {
            background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .step-icon.completed {
            background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
            color: white;
        }
        
        .step-icon.error {
            background: linear-gradient(135deg, #F56C6C 0%, #F78989 100%);
            color: white;
        }
        
        /* Tooltip样式 */
        .step-tooltip {
            max-width: 300px !important;
            white-space: pre-line !important;
            line-height: 1.5 !important;
            padding: 12px 16px !important;
            font-size: 13px !important;
            background: #303133 !important;
            border-radius: 8px !important;
        }
        
        /* 状态文字样式优化 */
        .step-status.active {
            background: #FDF6EC;
            color: #E6A23C;
            font-style: italic;
            animation: fadeInOut 2s infinite;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        
        /* 步骤内容布局优化 */
        .process-step {
            display: flex;
            align-items: flex-start;
            padding: 15px 0;
            border-bottom: 1px solid #e4e7ed;
            position: relative;
        }
        
        .step-content {
            flex: 1;
            min-width: 0;
        }
        
        .step-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .step-desc {
            font-size: 12px;
            color: #606266;
            line-height: 1.4;
        }
        
        .step-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 10px;
        }
        
        .step-status.pending {
            background: #f0f0f0;
            color: #909399;
        }
        
        .step-status.active {
            background: #FDF6EC;
            color: #E6A23C;
        }
        
        .step-status.completed {
            background: #f0f9ff;
            color: #67C23A;
        }
        
        .step-status.error {
            background: #fef0f0;
            color: #F56C6C;
        }
        
        /* 按钮样式 */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 30px;
        }
        
        .generate-btn {
            width: 100%;
            height: 50px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 25px;
            background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(230, 162, 60, 0.4);
        }
        
        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .action-buttons .el-button {
            height: 50px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 25px;
        }
        
        .action-buttons .el-button--primary {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            border: none;
        }
        
        .action-buttons .el-button--primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #E6A23C;
            color: #E6A23C;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-btn:hover {
            background: #E6A23C;
            color: white;
            transform: translateY(-2px);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .mode-selector {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <a href="index.html" class="back-btn">
            <i class="el-icon-arrow-left"></i> 返回首页
        </a>
        
        <div class="header">
            <h1>案卷评查</h1>
            <p>智能案卷评查系统，基于AI技术进行全面质量检查</p>
        </div>
        
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html" class="breadcrumb-item">
                    <i class="breadcrumb-icon">🏠</i>
                    <span>首页</span>
                </a>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item current">
                    <i class="breadcrumb-icon">🔍</i>
                    <span>案卷评查</span>
                </span>
            </div>
            
            <div class="main-content">
                <div class="left-panel">
                    <div class="ai-assistant">
                        <div class="ai-title">🔍 AI评查助手</div>
                        <p style="color: #606266; line-height: 1.6;">基于大模型技术，智能评查案卷文书质量，进行文字校验、逻辑校验、数据校验和条文引用合理性校验，确保案卷规范性。</p>
                    </div>
                    
                    <div class="section-title">
                        <i class="el-icon-setting"></i>
                        评查模式
                    </div>
                    
                    <div class="review-mode">
                        <el-radio-group v-model="reviewMode" class="mode-selector">
                            <el-radio label="single" class="mode-option">
                                <div class="mode-content">
                                    <i class="el-icon-document"></i>
                                    <span>单独评查</span>
                                    <p>选择特定文档进行评查</p>
                                </div>
                            </el-radio>
                            <el-radio label="batch" class="mode-option">
                                <div class="mode-content">
                                    <i class="el-icon-files"></i>
                                    <span>批量评查</span>
                                    <p>一次性评查所有案卷文件</p>
                                </div>
                            </el-radio>
                        </el-radio-group>
                    </div>
                    
                    <div class="section-title">
                        <i class="el-icon-folder"></i>
                        案件信息
                    </div>
                    
                    <div class="case-info">
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">案件名称</label>
                                <div class="case-name-row">
                                    <el-select v-model="caseInfo.name" placeholder="请选择或输入案件名称" filterable allow-create @change="onCaseNameChange">
                                        <el-option v-for="caseItem in predefinedCases" :key="caseItem.name" :label="caseItem.name" :value="caseItem.name"></el-option>
                                    </el-select>
                                    <el-button v-if="isFormDisabled" type="warning" size="small" @click="resetForm" class="re-edit-btn">重新编辑</el-button>
                                </div>
                            </div>
                            <div class="form-item">
                                <label class="form-label">案件类型</label>
                                <el-select v-model="caseInfo.type" placeholder="请选择案件类型" :disabled="isFormDisabled">
                                    <el-option label="无证经营" value="无证经营"></el-option>
                                    <el-option label="销售假冒伪劣" value="销售假冒伪劣"></el-option>
                                    <el-option label="违规销售" value="违规销售"></el-option>
                                    <el-option label="其他违法" value="其他违法"></el-option>
                                </el-select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">当事人信息</label>
                                <el-input v-model="caseInfo.party" placeholder="请输入当事人姓名/企业名称" :disabled="isFormDisabled"></el-input>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-item full-width">
                                <label class="form-label">案件描述</label>
                                <el-input v-model="caseInfo.description" type="textarea" :rows="3" placeholder="请输入案件基本情况" :disabled="isFormDisabled"></el-input>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <el-button v-if="reviewCompleted" type="primary" @click="viewDetailedReport">
                            <i class="el-icon-view"></i>
                            查看详细报告
                        </el-button>
                        <el-button class="generate-btn" @click="startReview" :loading="reviewing" :disabled="reviewing">
                            <span v-if="!reviewing && !reviewCompleted">开始评查</span>
                            <span v-else-if="!reviewing && reviewCompleted">重新评查</span>
                            <span v-else>评查中...</span>
                        </el-button>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="section-title">
                        <i class="el-icon-cpu"></i>
                        评查流程
                    </div>
                    
                    <div class="process-container">
                        <div class="process-step" v-for="(step, index) in reviewSteps" :key="index">
                            <el-tooltip 
                                :content="getTooltipContent(step)" 
                                placement="right" 
                                :show-after="300"
                                popper-class="step-tooltip"
                            >
                                <div class="step-icon" :class="step.status">
                                    <i v-if="step.status === 'pending'" :class="step.icon" style="font-size: 14px;"></i>
                                    <i v-else-if="step.status === 'active'" class="el-icon-loading"></i>
                                    <span v-else-if="step.status === 'completed'">✓</span>
                                    <i v-else-if="step.status === 'error'" class="el-icon-close"></i>
                                </div>
                            </el-tooltip>
                            <div class="step-content">
                                <div class="step-title">{{ step.title }}</div>
                                <div class="step-desc">{{ step.description }}</div>
                            </div>
                            <div class="step-status" :class="step.status">
                                <span v-if="step.status === 'pending'">等待中</span>
                                <span v-else-if="step.status === 'active'">{{ step.thinkingProcess }}</span>
                                <span v-else-if="step.status === 'completed'">已完成</span>
                                <span v-else-if="step.status === 'error'">失败</span>
                            </div>
                        </div>
                    </div>
                    
                    <div v-if="reviewCompleted" class="section-title" style="margin-top: 30px;">
                        <i class="el-icon-document"></i>
                        评查结果
                    </div>
                    
                    <div v-if="reviewCompleted" class="review-result">
                        <el-alert title="评查完成" type="success" :closable="false" style="margin-bottom: 15px;">
                            <template #default>
                                <p>案卷评查已完成，共发现 {{ reviewResult.issues }} 个问题，提供 {{ reviewResult.suggestions }} 条修改建议。</p>
                            </template>
                        </el-alert>
                        

                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        createApp({
            data() {
                return {
                    reviewMode: 'batch',
                    reviewing: false,
                    reviewCompleted: false,
                    currentStep: 0,
                    isFormDisabled: false,
                    caseInfo: {
                        name: '',
                        party: '',
                        type: '',
                        description: ''
                    },
                    predefinedCases: [
                        {
                            name: '张某无证经营烟草制品案',
                            party: '张某（个体经营者）',
                            type: '无证经营',
                            description: '当事人张某在未取得烟草专卖零售许可证的情况下，在其经营的便利店内销售卷烟，违反了《烟草专卖法》相关规定。'
                        },
                        {
                            name: '李某销售假冒卷烟案',
                            party: '李某（商店经营者）',
                            type: '销售假冒伪劣',
                            description: '当事人李某在其经营的烟酒店内销售假冒"中华"牌卷烟，涉嫌销售假冒注册商标的商品。'
                        },
                        {
                            name: '王某违规向未成年人售烟案',
                            party: '王某（超市负责人）',
                            type: '违规销售',
                            description: '当事人王某经营的超市向未满18周岁的未成年人销售烟草制品，违反了未成年人保护相关法律法规。'
                        },
                        {
                            name: '某公司网络销售烟草制品案',
                            party: '某电商公司',
                            type: '其他违法',
                            description: '当事人某电商公司通过网络平台销售烟草制品，违反了烟草专卖法关于烟草制品销售渠道的相关规定。'
                        }
                    ],
                    reviewSteps: [
                        {
                            title: '获取电子卷宗',
                            description: '判断是否已生成案卷，如无将自动生成',
                            thinkingProcess: '系统正在检查案件是否已有电子卷宗...',
                            resultPreview: '✓ 已找到完整电子卷宗文件\n✓ 包含询问笔录、检查笔录等8个文件\n✓ 文件完整性验证通过',
                            status: 'pending'
                        },
                        {
                            title: '进行文字校验',
                            description: '检查文档中的错别字、语法错误等',
                            thinkingProcess: 'AI正在逐字检查文档内容，识别错别字和语法问题...',
                            resultPreview: '✓ 检查了1,247个字符\n⚠ 发现2处错别字\n⚠ 发现1处标点符号问题\n✓ 语法结构正确',
                            status: 'pending'
                        },
                        {
                            title: '进行逻辑校验',
                            description: '验证案件逻辑关系和事实一致性',
                            thinkingProcess: '分析案件事实逻辑链条，检查前后一致性...',
                            resultPreview: '✓ 时间线逻辑正确\n✓ 证据链完整\n⚠ 处罚金额计算需核实\n✓ 法条适用逻辑合理',
                            status: 'pending'
                        },
                        {
                            title: '进行数据校验',
                            description: '核实案件中的数据准确性',
                            thinkingProcess: '验证案件中的数字、日期、金额等关键数据...',
                            resultPreview: '✓ 身份证号格式正确\n✓ 案发时间合理\n⚠ 卷烟数量与清单不符\n✓ 罚款金额计算正确',
                            status: 'pending'
                        },
                        {
                            title: '进行条文引用合理性校验',
                            description: '检查法律条文引用的准确性和适用性',
                            thinkingProcess: '核查引用法条的准确性和适用性...',
                            resultPreview: '✓ 《烟草专卖法》第23条引用正确\n✓ 《实施条例》条文适用合理\n✓ 处罚依据充分\n✓ 法条版本为最新版',
                            status: 'pending'
                        },
                        {
                            title: '生成评查结果',
                            description: '汇总所有校验结果',
                            thinkingProcess: '汇总分析所有检查结果，生成综合评价...',
                            resultPreview: '📊 文字问题：3个\n📊 逻辑问题：1个\n📊 数据问题：1个\n📊 法条问题：0个\n📊 总体评分：85分',
                            status: 'pending'
                        },
                        {
                            title: '提供修改建议',
                            description: '基于发现的问题提供具体修改建议',
                            thinkingProcess: '基于发现的问题生成具体可操作的修改建议...',
                            resultPreview: '💡 建议1：修正第3页错别字\"烟草\"\n💡 建议2：核实卷烟数量统计\n💡 建议3：补充证据收集时间\n💡 建议4：完善当事人信息',
                            status: 'pending'
                        },
                        {
                            title: '生成评查报告',
                            description: '生成完整的评查报告文档',
                            thinkingProcess: '整理所有评查内容，生成标准化报告文档...',
                            resultPreview: '📄 评查报告已生成\n📄 包含问题清单\n📄 包含修改建议\n📄 包含评分详情\n📄 支持导出PDF格式',
                            status: 'pending'
                        }
                    ],
                    reviewResult: {
                        issues: 0,
                        suggestions: 0
                    }
                };
            },
            mounted() {
                // 检查是否有从案件查询页面传递的案件信息
                const selectedCase = localStorage.getItem('selectedCase');
                if (selectedCase) {
                    try {
                        const caseData = JSON.parse(selectedCase);
                        // 自动填充案件信息
                        this.caseInfo.name = caseData.caseNumber || '';
                        this.caseInfo.party = caseData.partyName || '';
                        this.caseInfo.type = this.getCaseTypeText(caseData.caseType) || '';
                        this.caseInfo.description = caseData.description || `案件编号：${caseData.caseNumber}\n当事人：${caseData.partyName}\n办案人员：${caseData.officer}\n创建时间：${caseData.createTime}`;
                        
                        // 设置表单为可编辑状态
                        this.isFormDisabled = false;
                        
                        ElMessage.success('已自动加载案件信息，请确认后开始评查');
                        
                        // 清除localStorage中的案件信息，避免重复使用
                        localStorage.removeItem('selectedCase');
                    } catch (error) {
                        console.error('解析案件信息失败:', error);
                        ElMessage.warning('案件信息格式错误，请手动填写');
                    }
                }
            },
            methods: {
                // 获取案件类型文本
                getCaseTypeText(type) {
                    const typeMap = {
                        'unlicensed': '无证经营',
                        'beyond_scope': '超范围经营',
                        'fake_cigarettes': '销售假冒伪劣',
                        'other': '其他违法'
                    };
                    return typeMap[type] || type;
                },
                startReview() {
                    if (!this.validateForm()) {
                        return;
                    }
                    
                    this.reviewing = true;
                    this.reviewCompleted = false;
                    this.currentStep = 0;
                    
                    // 重置所有步骤状态
                    this.reviewSteps.forEach(step => {
                        step.status = 'pending';
                    });
                    
                    // 重置评查结果
                    this.reviewResult = {
                        issues: 0,
                        suggestions: 0
                    };
                    
                    // 模拟评查流程
                    this.simulateReviewProcess();
                },
                getTooltipContent(step) {
                    let content = `🔍 ${step.title}\n\n`;
                    
                    if (step.status === 'pending') {
                        content += `📋 准备阶段\n${step.description}`;
                    } else if (step.status === 'active') {
                        content += `⚡ 执行中\n${step.thinkingProcess}`;
                    } else if (step.status === 'completed') {
                        content += `✅ 执行结果\n${step.resultPreview}`;
                    } else if (step.status === 'error') {
                        content += `❌ 执行失败\n请检查案件信息后重试`;
                    }
                    
                    return content;
                },
                simulateReviewProcess() {
                    const stepDurations = [1000, 2000, 2500, 2000, 3000, 1500, 1000, 1000]; // 每个步骤的持续时间
                    let totalTime = 0;
                    
                    stepDurations.forEach((duration, index) => {
                        setTimeout(() => {
                            // 设置当前步骤为活动状态
                            if (index < this.reviewSteps.length) {
                                this.reviewSteps[index].status = 'active';
                            }
                        }, totalTime);
                        
                        totalTime += duration;
                        
                        setTimeout(() => {
                            // 设置当前步骤为完成状态
                            if (index < this.reviewSteps.length) {
                                this.reviewSteps[index].status = 'completed';
                            }
                            
                            // 如果是最后一个步骤，完成评查
                            if (index === stepDurations.length - 1) {
                                this.reviewing = false;
                                this.reviewCompleted = true;
                                this.reviewResult.issues = Math.floor(Math.random() * 5) + 1;
                                this.reviewResult.suggestions = Math.floor(Math.random() * 8) + 3;
                                ElMessage.success('案卷评查完成！');
                            }
                        }, totalTime);
                    });
                },
                
                validateForm() {
                    if (!this.caseInfo.name.trim()) {
                        ElMessage.warning('请输入案件名称');
                        return false;
                    }
                    if (!this.caseInfo.party.trim()) {
                        ElMessage.warning('请输入当事人信息');
                        return false;
                    }
                    if (!this.caseInfo.type) {
                        ElMessage.warning('请选择案件类型');
                        return false;
                    }
                    if (!this.caseInfo.description.trim()) {
                        ElMessage.warning('请输入案件描述');
                        return false;
                    }
                    return true;
                },
                
                onCaseNameChange(selectedName) {
                    const selectedCase = this.predefinedCases.find(caseItem => caseItem.name === selectedName);
                    
                    if (selectedCase) {
                        this.caseInfo.party = selectedCase.party;
                        this.caseInfo.type = selectedCase.type;
                        this.caseInfo.description = selectedCase.description;
                        this.isFormDisabled = true;
                        ElMessage.success('已自动填写案件信息，其他字段已锁定');
                    } else {
                        this.caseInfo.party = '';
                        this.caseInfo.type = '';
                        this.caseInfo.description = '';
                        this.isFormDisabled = false;
                        if (selectedName && selectedName.trim()) {
                            ElMessage.info('请手动填写案件信息');
                        }
                    }
                },
                
                resetForm() {
                    this.caseInfo = {
                        name: '',
                        party: '',
                        type: '',
                        description: ''
                    };
                    this.isFormDisabled = false;
                    ElMessage.info('表单已重置，可重新编辑');
                },
                
                viewDetailedReport() {
                    ElMessage.success('正在跳转到详细报告页面...');
                    setTimeout(() => {
                        window.location.href = 'case-review-detail.html';
                    }, 1000);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>