<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案卷详情 - 专卖案件助手</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c8cd;
            border-radius: 3px;
            transition: background 0.2s;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8b1ba;
        }

        /* Firefox滚动条样式 */
        * {
            scrollbar-width: thin;
            scrollbar-color: #c1c8cd #f1f3f4;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 8px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            min-width: 900px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 260px 1fr 280px;
            gap: 8px;
            height: calc(100vh - 16px);
            overflow-x: auto;
        }

        .file-sidebar {
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e6f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .file-search {
            margin-bottom: 10px;
        }

        .file-list {
            flex: 1;
            overflow-y: auto;
        }

        .file-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-item:hover {
            background-color: #f8f9fa;
        }

        .file-item.active {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }

        .file-main-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 0;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .file-type {
            font-size: 11px;
            color: #909399;
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 10px;
            white-space: nowrap;
            font-weight: 500;
        }

        .file-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 8px;
            white-space: nowrap;
            font-weight: 600;
        }

        .file-status.completed {
            background: #f0f9ff;
            color: #67c23a;
        }

        .file-status.draft {
            background: #fef0e6;
            color: #e6a23c;
        }

        .file-actions {
            display: flex;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
        }

        .file-actions .el-button {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            min-height: 24px;
        }

        .scan-icon {
            font-size: 14px;
            color: #409EFF;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .scan-icon:hover {
            background-color: #ecf5ff;
            transform: scale(1.1);
        }

        /* 扫描件预览弹窗样式 */
        .scan-preview-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .scan-image-wrapper {
            width: 100%;
            max-width: 800px;
            text-align: center;
        }

        .scan-image {
            max-width: 100%;
            max-height: 600px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .scan-info {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
        }

        .scan-info h3 {
            margin-bottom: 12px;
            color: #303133;
            font-size: 18px;
        }

        .scan-info p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
        }

        .dialog-footer {
            text-align: center;
        }

        /* 只读状态样式 */
        .el-input.is-disabled .el-input__inner,
        .el-textarea.is-disabled .el-textarea__inner {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #606266;
            cursor: not-allowed;
        }

        .el-input__inner[readonly],
        .el-textarea__inner[readonly] {
            background-color: #f8f9fa;
            border-color: #e4e7ed;
            color: #606266;
        }

        /* 编辑按钮样式 */
        .section-edit-btn {
            transition: all 0.3s ease;
        }

        .section-edit-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .main-content {
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e6f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 面包屑样式 */
        .breadcrumb {
            background: #ffffff;
            padding: 8px 16px;
            border-bottom: 1px solid #e1e6f0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;
            text-decoration: none;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: #409EFF;
        }

        .breadcrumb-item.current {
            color: #303133;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #C0C4CC;
            margin: 0 4px;
        }

        .breadcrumb-icon {
            font-size: 16px;
        }

        .header {
            background: #f8fbff;
            padding: 12px 16px;
            border-bottom: 1px solid #e1e6f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .document-title {
            font-size: 28px;
            font-weight: 700;
            color: #1a73e8;
            letter-spacing: 0.5px;
        }

        .case-number {
            color: #6c757d;
            font-size: 15px;
            margin-top: 4px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .content-area {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: white;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .info-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e6f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        .card-header {
            background: #4285f4;
            color: white;
            padding: 10px 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 15px;
            letter-spacing: 0.3px;
        }

        .card-content {
            padding: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 600;
            text-align: right;
            font-size: 14px;
        }

        .progress-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .progress-item:last-child {
            border-bottom: none;
        }

        .progress-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .progress-icon.completed {
            background: #34a853;
        }

        .progress-icon.current {
            background: #4285f4;
        }

        .progress-icon.pending {
            background: #e4e7ed;
            color: #909399;
        }

        .progress-text {
            flex: 1;
            font-size: 14px;
            color: #2c3e50;
        }

        .progress-time {
            font-size: 12px;
            color: #909399;
        }

        .document-content {
            line-height: 1.8;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 400;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1a73e8;
            margin: 20px 0 12px 0;
            padding-bottom: 6px;
            border-bottom: 2px solid #4285f4;
            letter-spacing: 0.5px;
        }

        .party-info {
            background: #f8fbff;
            border: 1px solid #e1e6f0;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
        }

        .party-info h4 {
            color: #1a73e8;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        .party-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        .violation-content {
            background: #f8fbff;
            border: 1px solid #e1e6f0;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }

        .penalty-content {
            background: #f8fbff;
            border: 1px solid #e1e6f0;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }

        .law-reference {
            background: #f8fbff;
            border: 1px solid #e1e6f0;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }

        .history-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #409eff;
        }

        .history-content {
            flex: 1;
        }

        .history-action {
            font-weight: 500;
            color: #2c3e50;
        }

        .history-time {
            font-size: 12px;
            color: #909399;
            margin-top: 2px;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .party-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 左侧文件列表 -->
            <div class="file-sidebar">
                <h3 style="margin: 0 0 15px 0; font-size: 16px; color: #303133;">案卷文件</h3>
                <div class="file-search">
                    <el-input 
                        v-model="fileSearchKeyword" 
                        placeholder="搜索文件..." 
                        prefix-icon="el-icon-search"
                        size="small"
                        clearable>
                    </el-input>
                </div>
                <div class="file-list">
                    <div 
                        v-for="file in filteredFiles" 
                        :key="file.id" 
                        class="file-item" 
                        :class="{ active: selectedFileId === file.id }"
                        @click="selectFile(file)"
                    >
                        <div class="file-main-info">
                            <span class="file-name" :title="file.name">{{ file.type }}</span>
                        </div>
                        <div class="file-actions" @click.stop>
                            <el-button 
                                v-if="file.hasScan" 
                                type="primary" 
                                size="small" 
                                @click="previewScan(file)"
                            >
                             查看扫描件
                            </el-button>
                            <el-button 
                                type="default" 
                                size="small" 
                                @click="downloadFile(file)"
                            >
                            下载
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 扫描件预览弹窗 -->
            <el-dialog
                v-model="showScanPreview"
                title="扫描件预览"
                width="80%"
                :before-close="closeScanPreview">
                <div class="scan-preview-container" v-if="currentScanFile">
                    <div class="scan-image-wrapper">
                        <img 
                            :src="currentScanFile.scanUrl" 
                            :alt="currentScanFile.name + '扫描件'"
                            class="scan-image"
                            @error="handleImageError" />
                    </div>
                    <div class="scan-info">
                        <h3>{{ currentScanFile.name }}</h3>
                        <p><strong>文件类型：</strong>{{ currentScanFile.type }}</p>
                        <p><strong>状态：</strong>{{ currentScanFile.status === 'completed' ? '已完成' : '草稿' }}</p>
                    </div>
                </div>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="closeScanPreview">关闭</el-button>
                        <el-button type="primary" @click="downloadScan(currentScanFile)" v-if="currentScanFile">
                            下载扫描件
                        </el-button>
                    </div>
                </template>
            </el-dialog>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 面包屑导航 -->
                <div class="breadcrumb">
                    <a href="index.html" class="breadcrumb-item">
                        <i class="breadcrumb-icon">🏠</i>
                        <span>首页</span>
                    </a>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item current">
                        <i class="breadcrumb-icon">📄</i>
                        <span>案卷详情</span>
                    </span>
                </div>
                
                <div class="header">
                    <div>
                        <div class="document-title">行政处罚决定书</div>
                        <div class="case-number">惠阳烟处（2025）第5号</div>
                        <div style="margin-top: 8px;">
                            <el-tag type="info" size="small" style="margin-right: 8px;">案件名称</el-tag>
                            <span style="font-size: 14px; color: #606266; font-weight: 500;">{{ caseInfo.caseName || '徐某未取得烟草专卖零售许可证经营烟草专卖品案' }}</span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <el-tooltip content="批量导出案件相关文书" placement="bottom">
                            <el-button type="success" @click="exportDocument">
                                导出案件
                            </el-button>
                        </el-tooltip>
                    </div>
                </div>
                
                <div class="content-area">
                    <div class="document-content">
                        <!-- 当事人信息 -->
                        <div class="party-info">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <h4 style="margin: 0; color: #303133; font-size: 16px;">🏢 当事人信息</h4>
                                <el-button 
                                    class="section-edit-btn"
                                    :type="editingStates.partyInfo ? 'success' : 'primary'" 
                                    size="small" 
                                    @click="toggleEdit('partyInfo')">
                                    <el-icon><Edit /></el-icon>
                                    {{ editingStates.partyInfo ? '保存' : '编辑' }}
                                </el-button>
                            </div>
                            <el-form :model="partyInfo" label-width="100px" size="small" label-position="left">
                                <el-row :gutter="16">
                                    <el-col :span="12">
                                        <el-form-item label="当事人">
                                            <el-input v-model="partyInfo.name" placeholder="请输入当事人姓名" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证号">
                                            <el-input v-model="partyInfo.idCard" placeholder="请输入身份证号" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="16">
                                    <el-col :span="12">
                                        <el-form-item label="联系电话">
                                            <el-input v-model="partyInfo.phone" placeholder="请输入联系电话" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="法定代表人">
                                            <el-input v-model="partyInfo.legalPerson" placeholder="请输入法定代表人" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="16">
                                    <el-col :span="24">
                                        <el-form-item label="住址">
                                            <el-input v-model="partyInfo.address" placeholder="请输入住址" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="16">
                                    <el-col :span="12">
                                        <el-form-item label="营业执照号">
                                            <el-input v-model="partyInfo.businessLicense" placeholder="请输入营业执照号" :readonly="!editingStates.partyInfo"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>

                        <!-- 违法事实与证据 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0 12px 0;">
                            <div class="section-title" style="margin: 0;">违法事实与证据</div>
                            <el-button 
                                class="section-edit-btn"
                                :type="editingStates.violation ? 'success' : 'primary'" 
                                size="small" 
                                @click="toggleEdit('violation')">
                                <el-icon><Edit /></el-icon>
                                {{ editingStates.violation ? '保存' : '编辑' }}
                            </el-button>
                        </div>
                        <div class="violation-content">
                            <el-input 
                                v-model="documentContent.violation" 
                                type="textarea" 
                                :rows="4" 
                                placeholder="请输入违法事实与证据内容"
                                :readonly="!editingStates.violation">
                            </el-input>
                        </div>

                        <!-- 法律依据 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0 12px 0;">
                            <div class="section-title" style="margin: 0;">法律依据</div>
                            <el-button 
                                class="section-edit-btn"
                                :type="editingStates.lawBasis ? 'success' : 'primary'" 
                                size="small" 
                                @click="toggleEdit('lawBasis')">
                                <el-icon><Edit /></el-icon>
                                {{ editingStates.lawBasis ? '保存' : '编辑' }}
                            </el-button>
                        </div>
                        <div class="law-reference">
                            <el-input 
                                v-model="documentContent.lawBasis" 
                                type="textarea" 
                                :rows="3" 
                                placeholder="请输入法律依据内容"
                                :readonly="!editingStates.lawBasis">
                            </el-input>
                        </div>

                        <!-- 处罚决定 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0 12px 0;">
                            <div class="section-title" style="margin: 0;">处罚决定</div>
                            <el-button 
                                class="section-edit-btn"
                                :type="editingStates.penalty ? 'success' : 'primary'" 
                                size="small" 
                                @click="toggleEdit('penalty')">
                                <el-icon><Edit /></el-icon>
                                {{ editingStates.penalty ? '保存' : '编辑' }}
                            </el-button>
                        </div>
                        <div class="penalty-content">
                            <el-input 
                                v-model="documentContent.penalty" 
                                type="textarea" 
                                :rows="5" 
                                placeholder="请输入处罚决定内容"
                                :readonly="!editingStates.penalty">
                            </el-input>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 基本信息汇总 -->
                <div class="info-card">
                    <div class="card-header">
                        <i class="el-icon-user"></i>
                        基本信息汇总
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <span class="info-label">案件类型：</span>
                            <span class="info-value">未取得烟草专卖零售许可证经营烟草专卖品</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">违法主体：</span>
                            <span class="info-value">惠州市惠阳区淡水某某商贸行</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">案发时间：</span>
                            <span class="info-value">2025-01-04 10:30</span>
                        </div>
                    </div>
                </div>

                <!-- 案件事实汇总 -->
                <div class="info-card">
                    <div class="card-header">
                        <i class="el-icon-warning"></i>
                        案件事实汇总
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <span class="info-label">主要事实：</span>
                        </div>
                        <div style="margin-top: 10px; font-size: 14px; color: #495057;">
                            <div>• 未取得烟草专卖零售许可证经营烟草专卖品</div>
                            <div>• 现场查获卷烟32个品牌规格共计1325条</div>
                            <div>• 包括黄山（新一代）100条、黄山（硬）75条等</div>
                            <div>• 当事人无法提供供货渠道的合法来源证明</div>
                        </div>
                    </div>
                </div>

                <!-- 法律条款汇总 -->
                <div class="info-card">
                    <div class="card-header">
                        <i class="el-icon-document"></i>
                        法律条款汇总
                    </div>
                    <div class="card-content">
                        <div style="font-size: 14px; color: #495057;">
                            <div>• 《烟草专卖法》（第31条第1款）</div>
                            <div>• 《烟草专卖法实施条例》（第59条第1款）</div>
                            <div>• 《烟草专卖行政处罚程序规定》（第24条第1款）</div>
                        </div>
                    </div>
                </div>

                <!-- 历史关联信息 -->
                <div class="info-card">
                    <div class="card-header">
                        <i class="el-icon-time"></i>
                        历史关联信息
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <span class="info-label">关联案件：</span>
                            <span class="info-value">无</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">历史记录：</span>
                            <span class="info-value">首次违法</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">风险等级：</span>
                            <span class="info-value" style="color: #e6a23c;">中等</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script>
        const { createApp } = Vue;
        const { ElButton, ElTooltip, ElMessage, ElInput, ElForm, ElFormItem, ElRow, ElCol, ElTag, ElIcon } = ElementPlus;

        createApp({
            components: {
                ElButton,
                ElTooltip,
                ElInput,
                ElForm,
                ElFormItem,
                ElRow,
                ElCol,
                ElTag,
                ElIcon
            },
            data() {
                return {
                    caseInfo: {
                        title: '行政处罚决定书',
                        caseNumber: '惠阳烟处（2025）第5号',
                        caseName: '徐某未取得烟草专卖零售许可证经营烟草专卖品案',
                        accuracy: '95%'
                    },
                    partyInfo: {
                        name: '徐伟雄，男，汉族',
                        idCard: '4452***********19',
                        phone: '1836****375',
                        address: '广东省惠州市惠阳区淡水某某商贸中心A1楼2号',
                        businessLicense: '4413***********60',
                        legalPerson: '徐伟雄'
                    },
                    documentContent: {
                        violation: '本在当地烟草专卖批发企业进货。2025年1月4日10时30分，我局执法人员黄国强（执法证号：1909******）、董玉凤（执法证号：1909******）出示证件，表明身份，对市场摊位所有者惠州市惠阳区淡水某某商贸行（烟草专卖零售许可证号：4413******）进行检查，发现该场所有涉嫌本地烟草专卖批发企业进货的卷烟。在其场所内查获黄山（新一代）100条、黄金叶（蓝）100条、黄山（硬）75条、红河（硬）100条、红双喜（硬经典）525条共计9个品牌规格合计1325条。该场所未能提供合法有效证明。',
                        lawBasis: '依据《中华人民共和国烟草专卖法》第二十三条第二款的规定，有未在当地烟草专卖批发企业进货的行为。经我局依法立案调查，我局依据《中华人民共和国烟草专卖法实施条例》（2025）第5号对该案进行了立案调查。',
                        penalty: '1. 没收违法所得：没收涉案卷烟9个品牌规格共计1325条；\n2. 处以罚款：处以罚款人民币元（¥**,***.00）元的罚款。\n当事人应当自收到本处罚决定书之日起十五日内，将罚款缴至指定账户（账号：************）。逾期不缴纳的，每日按罚款数额的百分之三加处罚款。'
                    },
                    fileSearchKeyword: '',
                    selectedFileId: 4,
                    showScanPreview: false,
                    currentScanFile: null,
                    editingStates: {
                        partyInfo: false,
                        violation: false,
                        lawBasis: false,
                        penalty: false
                    },
                    caseFiles: [
                        { id: 1, name: '询问笔录-徐某', type: '询问笔录', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/询问笔录扫描件.jpg' },
                        { id: 2, name: '检查笔录-现场检查', type: '检查笔录', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/检查笔录扫描件.jpg' },
                        { id: 3, name: '扣押清单-卷烟1325条', type: '扣押清单', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/扣押清单扫描件.jpg' },
                        { id: 4, name: '处罚决定书-徐某案', type: '处罚决定书', status: 'completed', hasScan: false },
                        { id: 5, name: '告知书-行政处罚', type: '告知书', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/告知书扫描件.jpg' },
                        { id: 6, name: '现场照片-证据材料', type: '现场照片', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/现场照片扫描件.jpg' },
                        { id: 7, name: '身份证复印件-徐某', type: '身份证明', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/身份证扫描件.jpg' },
                        { id: 8, name: '营业执照复印件-商贸行', type: '营业执照', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/营业执照扫描件.jpg' },
                        { id: 9, name: '烟草专卖零售许可证', type: '许可证', status: 'completed', hasScan: false },
                        { id: 10, name: '案件移送书', type: '移送材料', status: 'completed', hasScan: true, scanUrl: 'assets/images/scan-samples/移送书扫描件.jpg' },
                        { id: 11, name: '听证笔录-草稿', type: '听证笔录', status: 'draft', hasScan: false },
                        { id: 12, name: '复议申请书', type: '复议申请', status: 'draft', hasScan: false }
                    ]
                };
            },
            computed: {
                filteredFiles() {
                    if (!this.fileSearchKeyword) {
                        return this.caseFiles;
                    }
                    return this.caseFiles.filter(file => 
                        file.name.toLowerCase().includes(this.fileSearchKeyword.toLowerCase()) ||
                        file.type.toLowerCase().includes(this.fileSearchKeyword.toLowerCase())
                    );
                }
            },
            mounted() {
                // 从localStorage获取传递过来的案件信息
                const caseData = localStorage.getItem('currentCaseData');
                const fileData = localStorage.getItem('currentFileData');
                
                if (caseData) {
                    try {
                        const parsedCaseData = JSON.parse(caseData);
                        // 更新案件基本信息
                        this.caseInfo.caseName = parsedCaseData.caseName || this.caseInfo.caseName;
                        this.caseInfo.caseNumber = parsedCaseData.caseNumber || this.caseInfo.caseNumber;
                        
                        // 更新当事人信息
                        if (parsedCaseData.partyName) {
                            this.partyInfo.name = parsedCaseData.partyName;
                        }
                        if (parsedCaseData.idCard) {
                            this.partyInfo.idCard = parsedCaseData.idCard;
                        }
                        if (parsedCaseData.address) {
                            this.partyInfo.address = parsedCaseData.address;
                        }
                        
                        ElMessage.success(`已加载案件：${this.caseInfo.caseName}`);
                        
                        // 清除localStorage中的数据
                        localStorage.removeItem('currentCaseData');
                    } catch (error) {
                        console.error('解析案件数据失败:', error);
                        ElMessage.warning('案件数据加载失败，显示默认数据');
                    }
                }
                
                if (fileData) {
                    try {
                        const parsedFileData = JSON.parse(fileData);
                        // 如果有特定文件信息，可以在这里处理
                        console.log('文件数据:', parsedFileData);
                        
                        // 清除localStorage中的数据
                        localStorage.removeItem('currentFileData');
                    } catch (error) {
                        console.error('解析文件数据失败:', error);
                    }
                }
            },
            methods: {
                selectFile(file) {
                    this.selectedFileId = file.id;
                    ElMessage.success(`已打开文件：${file.name}`);
                },
                editDocument() {
                    ElMessage.info('编辑功能开发中...');
                },
                exportDocument() {
                    ElMessage.success('文档导出成功！');
                },
                previewScan(file) {
                    this.currentScanFile = file;
                    this.showScanPreview = true;
                },
                closeScanPreview() {
                    this.showScanPreview = false;
                    this.currentScanFile = null;
                },
                downloadScan(file) {
                    const link = document.createElement('a');
                    link.href = file.scanUrl;
                    link.download = `${file.name}_扫描件.jpg`;
                    link.click();
                    ElMessage.success('扫描件下载成功！');
                },
                downloadFile(file) {
                    ElMessage.success(`${file.name} 下载成功！`);
                },
                handleImageError() {
                    ElMessage.warning('扫描件加载失败，请稍后重试');
                },
                toggleEdit(section) {
                    this.editingStates[section] = !this.editingStates[section];
                    if (this.editingStates[section]) {
                        ElMessage.success(`已开启${this.getSectionName(section)}编辑模式`);
                    } else {
                        ElMessage.success(`已保存${this.getSectionName(section)}修改`);
                    }
                },
                getSectionName(section) {
                    const names = {
                        partyInfo: '当事人信息',
                        violation: '违法事实与证据',
                        lawBasis: '法律依据',
                        penalty: '处罚决定'
                    };
                    return names[section] || section;
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>