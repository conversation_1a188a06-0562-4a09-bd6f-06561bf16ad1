<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案卷评查详情 - 专卖案件文书大模型智办系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f3f4;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c8cd;
            border-radius: 3px;
            transition: background 0.2s;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8b1ba;
        }

        /* Firefox滚动条样式 */
        * {
            scrollbar-width: thin;
            scrollbar-color: #c1c8cd #f1f3f4;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.6;
            color: #303133;
        }
        
        .header {
            text-align: center;
            padding: 50px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }
        
        .header-text {
            text-align: left;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .export-btn {
            background: rgba(255, 255, 255, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .export-btn:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .export-btn i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .header h1 {
            margin: 0 0 15px 0;
            font-size: 32px;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 0;
            font-size: 18px;
            opacity: 0.95;
            position: relative;
            z-index: 1;
            font-weight: 300;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
            max-width: 1400px;
            margin: 0 auto;
            gap: 0;
        }
        
        /* 左侧文件列表 */
        .left-sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #e4e7ed;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
            z-index: 10;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e4e7ed;
            background: #fafafa;
        }
        
        .sidebar-title {
            font-size: 17px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        }
        
        .case-info {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
        }
        
        .file-list {
            flex: 1;
            overflow-y: auto;
        }
        
        .file-category {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-header {
            padding: 15px 20px;
            background: #f8f9fa;
            font-weight: 600;
            color: #303133;
            font-size: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.2s;
            letter-spacing: 0.3px;
        }
        
        .category-header:hover {
            background: #f0f0f0;
        }
        
        .category-icon {
            transition: transform 0.2s;
        }
        
        .category-header.collapsed .category-icon {
            transform: rotate(-90deg);
        }
        
        .file-items {
            max-height: 300px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .file-items.collapsed {
            max-height: 0;
        }
        
        .file-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f5f5f5;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-item:hover {
            background: #f0f9ff;
        }
        
        .file-item.active {
            background: #e6f7ff;
            border-right: 3px solid #409eff;
        }
        
        .file-icon {
            font-size: 16px;
            color: #409eff;
        }
        
        .file-name {
            font-size: 14px;
            color: #303133;
            flex: 1;
            font-weight: 500;
        }
        
        .file-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .scan-icon {
            font-size: 16px;
            color: #67c23a;
            cursor: pointer;
            transition: all 0.2s;
            padding: 4px;
            border-radius: 4px;
        }
        
        .scan-icon:hover {
            background: #f0f9ff;
            transform: scale(1.1);
        }
        
        .file-status {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .file-status.normal {
            background: #f0f9ff;
            color: #409eff;
        }
        
        .file-status.warning {
            background: #fdf6ec;
            color: #e6a23c;
        }
        
        .file-status.error {
            background: #fef0f0;
            color: #f56c6c;
        }
        
        /* 扫描件预览样式 */
        .scan-preview-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .scan-image-wrapper {
            text-align: center;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
        }
        
        .scan-image {
            max-width: 100%;
            max-height: 600px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .scan-image:hover {
            transform: scale(1.02);
        }
        
        .scan-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e4e7ed;
        }
        
        .scan-info p {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .scan-info strong {
            color: #303133;
            font-weight: 600;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }
        
        .content-header {
            padding: 20px;
            border-bottom: 1px solid #e4e7ed;
            background: #fafafa;
        }
        
        .document-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
            text-align: center;
            letter-spacing: 0.5px;
        }
        
        .document-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .content-body {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            line-height: 1.8;
        }
        
        .document-content {
            max-width: 800px;
            margin: 0 auto;
            font-size: 15px;
            color: #303133;
            line-height: 1.8;
        }
        
        .document-content h2 {
            text-align: center;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 25px;
            color: #303133;
            letter-spacing: 1px;
        }
        
        .document-content h3 {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 18px 0;
            color: #303133;
            letter-spacing: 0.5px;
        }
        
        .document-content p {
            margin-bottom: 18px;
            text-indent: 2em;
            line-height: 1.8;
        }
        
        .document-content .case-number {
            text-align: center;
            font-size: 14px;
            color: #606266;
            margin-bottom: 30px;
        }
        
        .party-info {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .party-info .label {
            display: inline-block;
            width: 80px;
            font-weight: 600;
        }
        
        .party-info .value {
            color: #606266;
        }
        
        /* 右侧评查结果面板 */
        .right-panel {
            width: 380px;
            background: white;
            border-left: 1px solid #e4e7ed;
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0,0,0,0.1);
            z-index: 10;
        }
        
        .panel-header {
            padding: 25px 20px;
            border-bottom: 1px solid #e4e7ed;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }
        
        .panel-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
        }
        
        .panel-title {
            font-size: 19px;
            font-weight: 700;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 10px;
            letter-spacing: 0.5px;
        }
        
        .panel-title::before {
            content: '📊';
            font-size: 20px;
        }
        
        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .review-summary {
            margin-bottom: 20px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .summary-label {
            font-size: 14px;
            color: #606266;
        }
        
        .summary-value {
            font-size: 16px;
            font-weight: 600;
        }
        
        .summary-value.normal {
            color: #67c23a;
        }
        
        .summary-value.warning {
            color: #e6a23c;
        }
        
        .summary-value.error {
            color: #f56c6c;
        }
        
        .issue-list {
            margin-top: 20px;
        }
        
        .issue-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .issue-item.warning {
            background: #fdf6ec;
            border-left-color: #e6a23c;
        }
        
        .issue-item.error {
            background: #fef0f0;
            border-left-color: #f56c6c;
        }
        
        .issue-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            letter-spacing: 0.3px;
        }
        
        .issue-desc {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
        }
        
        .issue-location {
            font-size: 12px;
            color: #909399;
            margin-top: 6px;
            font-weight: 500;
        }
        
        /* 修改建议样式 */
        .suggestion-list {
            margin-top: 20px;
        }
        
        .suggestion-item {
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .suggestion-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .suggestion-number {
            font-size: 14px;
            font-weight: 600;
            color: #409eff;
            margin-right: 8px;
            min-width: 20px;
        }
        
        .suggestion-title {
            font-size: 15px;
            font-weight: 600;
            color: #303133;
            letter-spacing: 0.3px;
        }
        
        .suggestion-desc {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
            margin-bottom: 10px;
            padding-left: 28px;
        }
        
        .suggestion-location {
            font-size: 12px;
            color: #909399;
            margin-bottom: 12px;
            padding-left: 28px;
            font-weight: 500;
        }
        
        .suggestion-actions {
            display: flex;
            gap: 10px;
            padding-left: 28px;
        }
        
        .suggestion-btn {
            padding: 6px 16px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .suggestion-btn.accept {
            background: #409eff;
            color: white;
        }
        
        .suggestion-btn.accept:hover {
            background: #337ecc;
        }
        
        .suggestion-btn.ignore {
            background: #f0f0f0;
            color: #606266;
        }
        
        .suggestion-btn.ignore:hover {
             background: #e0e0e0;
         }
         
         /* 文档高亮样式 */
         .highlight {
             background: #fff3cd;
             border: 2px solid #ffc107;
             border-radius: 3px;
             padding: 2px 4px;
             animation: highlightPulse 2s ease-in-out;
         }
         
         @keyframes highlightPulse {
             0% { background: #fff3cd; }
             50% { background: #ffeaa7; }
             100% { background: #fff3cd; }
         }
         
         /* 建议项悬停效果 */
         .suggestion-item:hover {
             background: #f0f8ff;
             border-color: #409eff;
             cursor: pointer;
             transform: translateY(-2px);
             box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
         }
         
         .suggestion-item.active {
             background: #e8f4fd;
             border-color: #409eff;
             box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
         }
        
        /* 返回按钮 */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #E6A23C;
            color: #E6A23C;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }
        
        .back-btn:hover {
            background: #E6A23C;
            color: white;
            transform: translateY(-2px);
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .right-panel {
                width: 300px;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }
            
            .left-sidebar {
                width: 100%;
                height: 300px;
            }
            
            .right-panel {
                width: 100%;
                height: 400px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .header-text {
                text-align: center;
            }
            
            .export-btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <a href="case-review.html" class="back-btn">
            <i class="el-icon-arrow-left"></i> 返回评查
        </a>
        
        <div class="header">
            <div class="header-content">
                <div class="header-text">
                    <h1>案卷评查详情</h1>
                    <p>智能案卷评查系统 - 详细审查结果</p>
                </div>
                <div class="header-actions">
                    <el-button type="primary" size="large" @click="exportCaseFiles" class="export-btn">
                        <i class="el-icon-download"></i>
                        导出评查报告
                    </el-button>
                </div>
            </div>
        </div>
        
        <div class="container">
            <!-- 左侧文件列表 -->
            <div class="left-sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-title">案卷评查系统</div>
                    <div class="case-info">
                        <div>案件：{{ currentCase.name }}</div>
                        <div>立案时间：{{ currentCase.date }}</div>
                        <div>承办人：{{ currentCase.officer }}</div>
                        <div>当事人：{{ currentCase.party }}</div>
                    </div>
                </div>
                
                <div class="file-list">
                    <div class="file-item" 
                         :class="{ active: currentFile.id === file.id }"
                         v-for="file in fileList" 
                         :key="file.id"
                         @click="selectFile(file)">
                        <i class="file-icon">📄</i>
                        <span class="file-name">{{ file.name }}</span>
                        <div class="file-actions">
                            <i class="scan-icon" 
                               v-if="file.hasScan" 
                               @click.stop="previewScan(file)"
                               title="查看扫描件">
                                📷
                            </i>
                            <span class="file-status" :class="file.status">{{ getStatusText(file.status) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <div class="content-header">
                    <div class="document-title">{{ currentFile.title }}</div>
                    <div class="document-meta">
                        <span>立案人：{{ currentCase.officer }}</span>
                        <span>立案时间：{{ currentCase.date }}</span>
                        <span>文件时间：{{ currentFile.date }}</span>
                        <span>当事人：{{ currentCase.party }}</span>
                    </div>
                </div>
                
                <div class="content-body">
                    <div class="document-content" v-html="currentFile.content"></div>
                </div>
            </div>
            
            <!-- 右侧评查结果面板 -->
            <div class="right-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i style="color: #409eff;">🔍</i>
                        评查结果
                    </div>
                </div>
                
                <div class="panel-content">
                    <div class="review-summary">
                        <div class="summary-item">
                            <span class="summary-label">文档状态</span>
                            <span class="summary-value" :class="currentFile.status">{{ getStatusText(currentFile.status) }}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">发现问题</span>
                            <span class="summary-value error">{{ currentFile.issues.length }}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">修改建议</span>
                            <span class="summary-value warning">{{ currentFile.suggestions.length }}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">评查得分</span>
                            <span class="summary-value" :class="getScoreClass(currentFile.score)">{{ currentFile.score }}分</span>
                        </div>
                    </div>
                    
                    <div class="issue-list" v-if="currentFile.issues.length > 0">
                        <h4 style="margin-bottom: 15px; color: #303133;">发现的问题</h4>
                        <div class="issue-item" 
                             :class="issue.type" 
                             v-for="issue in currentFile.issues" 
                             :key="issue.id">
                            <div class="issue-title">{{ issue.title }}</div>
                            <div class="issue-desc">{{ issue.description }}</div>
                            <div class="issue-location">位置：{{ issue.location }}</div>
                        </div>
                    </div>
                    
                    <div class="suggestion-list" v-if="currentFile.suggestions.length > 0">
                        <h4 style="margin-bottom: 15px; color: #303133; margin-top: 20px;">修改建议 ({{ currentFile.suggestions.length }})</h4>
                        <div class="suggestion-item" 
                             :class="{ active: activeSuggestion === suggestion.id }"
                             v-for="(suggestion, index) in currentFile.suggestions" 
                             :key="suggestion.id"
                             @click="highlightSuggestion(suggestion)">
                            <div class="suggestion-header">
                                <span class="suggestion-number">{{ index + 1 }}.</span>
                                <span class="suggestion-title">{{ suggestion.title }}</span>
                            </div>
                            <div class="suggestion-desc">{{ suggestion.description }}</div>
                            <div class="suggestion-location">位置：{{ suggestion.location }}</div>
                            <!-- <div class="suggestion-actions" @click.stop>
                                <button class="suggestion-btn accept" @click="acceptSuggestion(suggestion)">采纳</button>
                                <button class="suggestion-btn ignore" @click="ignoreSuggestion(suggestion)">忽略</button>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 扫描件预览弹窗 -->
        <el-dialog 
            v-model="showScanPreview" 
            :title="currentScanFile ? `${currentScanFile.name} - 扫描件预览` : '扫描件预览'"
            width="80%"
            center
            @close="closeScanPreview">
            <div class="scan-preview-container" v-if="currentScanFile">
                <div class="scan-image-wrapper">
                    <img 
                        :src="currentScanFile.scanUrl" 
                        :alt="currentScanFile.name + '扫描件'"
                        class="scan-image"
                        @error="handleImageError" />
                </div>
                <div class="scan-info">
                    <p><strong>文件名称：</strong>{{ currentScanFile.name }}</p>
                    <p><strong>文件日期：</strong>{{ currentScanFile.date }}</p>
                    <p><strong>文件状态：</strong><span :class="currentScanFile.status">{{ getStatusText(currentScanFile.status) }}</span></p>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeScanPreview">关闭</el-button>
                    <el-button type="primary" @click="downloadScan" v-if="currentScanFile">
                        <el-icon><Download /></el-icon>
                        下载扫描件
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    currentCase: {
                        name: '张某无证经营烟草制品案',
                        date: '2024-01-15',
                        officer: '李三',
                        party: '张某'
                    },
                    currentFile: {
                        id: 0,
                        name: '',
                        title: '',
                        date: '',
                        status: 'normal',
                        score: 0,
                        issues: [],
                        suggestions: [],
                        content: ''
                    },
                    activeSuggestion: null,
                    showScanPreview: false,
                    currentScanFile: null,
                    fileList: [
                        { id: 1, name: '举报记录表', title: '举报记录表', date: '2024-01-10', status: 'normal', score: 95, issues: [], suggestions: [], content: '<p>举报记录表内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=举报记录表扫描件' },
                        { id: 2, name: '立案报告表', title: '立案报告表', date: '2024-01-11', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>立案报告表内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=立案报告表扫描件' },
                        { id: 3, name: '不予立案报告表', title: '不予立案报告表', date: '2024-01-12', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>不予立案报告表内容...</p>', hasScan: false },
                        { id: 4, name: '不予立案告知书', title: '不予立案告知书', date: '2024-01-12', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>不予立案告知书内容...</p>', hasScan: false },
                        { id: 5, name: '延长立案期限审批表', title: '延长立案期限审批表', date: '2024-01-13', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>延长立案期限审批表内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=延长立案期限审批表扫描件' },
                        { id: 6, name: '延长立案期限告知书', title: '延长立案期限告知书', date: '2024-01-13', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>延长立案期限告知书内容...</p>', hasScan: false },
                        { id: 7, name: '指定管辖通知书', title: '指定管辖通知书', date: '2024-01-14', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>指定管辖通知书内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=指定管辖通知书扫描件' },
                        { id: 8, name: '现场笔录', title: '现场笔录', date: '2024-01-15', status: 'warning', score: 85, issues: [{ id: 1, type: 'warning', title: '时间记录不够详细', description: '检查开始和结束时间应该更加精确到分钟', location: '笔录开头时间部分' }], suggestions: [{ id: 1, title: '完善时间记录', description: '建议将检查时间精确到分钟，并记录每个重要环节的具体时间', location: '笔录时间栏' }], content: '<p>现场笔录内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=现场笔录扫描件' },
                        { id: 9, name: '电子数据证据提取笔录（附件电子数据清单）', title: '电子数据证据提取笔录', date: '2024-01-15', status: 'normal', score: 94, issues: [], suggestions: [], content: '<p>电子数据证据提取笔录内容...</p>', hasScan: false },
                        { id: 10, name: '证据先行登记保存批准书', title: '证据先行登记保存批准书', date: '2024-01-16', status: 'normal', score: 96, issues: [], suggestions: [], content: '<p>证据先行登记保存批准书内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=证据先行登记保存批准书扫描件' },
                        { id: 11, name: '证据先行登记保存通知书', title: '证据先行登记保存通知书', date: '2024-01-16', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>证据先行登记保存通知书内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=证据先行登记保存通知书扫描件' },
                        { id: 12, name: '先行登记保存证据处理通知书', title: '先行登记保存证据处理通知书', date: '2024-01-17', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>先行登记保存证据处理通知书内容...</p>', hasScan: false },
                        { id: 13, name: '抽样取证物品清单', title: '抽样取证物品清单', date: '2024-01-17', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>抽样取证物品清单内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=抽样取证物品清单扫描件' },
                        { id: 14, name: '鉴别检验留样告知书', title: '鉴别检验留样告知书', date: '2024-01-18', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>鉴别检验留样告知书内容...</p>', hasScan: false },
                        { id: 15, name: '询问通知书', title: '询问通知书', date: '2024-01-18', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>询问通知书内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=询问通知书扫描件' },
                        { id: 16, name: '询问笔录', title: '询问笔录', date: '2024-01-19', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>询问笔录内容...</p>', hasScan: true, scanUrl: 'https://via.placeholder.com/800x1000/f0f0f0/333?text=询问笔录扫描件' },
                        { id: 17, name: '证据复制（提取）单', title: '证据复制（提取）单', date: '2024-01-19', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>证据复制（提取）单内容...</p>', hasScan: false },
                        { 
                            id: 18, 
                            name: '涉案物品核价表', 
                            title: '涉案物品核价表', 
                            date: '2024-01-20', 
                            status: 'error', 
                            score: 75, 
                            issues: [{ 
                                id: 1, 
                                type: 'error', 
                                title: '物品数量统计错误', 
                                description: '清单中卷烟总数与实际扣押数量不符', 
                                location: '清单汇总部分' 
                            }], 
                            suggestions: [{ 
                                id: 1, 
                                title: '重新核对数量', 
                                description: '建议重新清点扣押物品数量，确保清单准确无误', 
                                location: '整个清单' 
                            }], 
                            content: `
                                <div style="text-align: center; margin-bottom: 30px;">
                                    <h2 style="margin: 0; font-size: 18px; font-weight: bold;">广东省惠州市烟草专卖局</h2>
                                    <h3 style="margin: 10px 0; font-size: 16px; font-weight: bold;">涉案物品核价表</h3>
                                    <p style="margin: 5px 0; font-size: 14px;">
                                        惠州市某商区烟草专卖局于2025年1月4日立案（立案编号：惠阳烟立（2025）第5号）的钟伟涉嫌未取得烟草专卖批发企业许可证件中，
                                        <span style="color: #e74c3c; font-weight: bold;">涉案物品</span>价格如下：
                                    </p>
                                </div>
                                <table style="width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 14px;">
                                    <thead>
                                        <tr style="background-color: #f8f9fa;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">序号</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">品种规格</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">数量（条）</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">单价（元）</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">合计（元）</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">1</td><td style="border: 1px solid #ddd; padding: 8px;">娇子(硬红)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">200.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">60.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">12000.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">2</td><td style="border: 1px solid #ddd; padding: 8px;">黄金叶(硬黄金)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">100.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">100.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">10000.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">3</td><td style="border: 1px solid #ddd; padding: 8px;">黄山(新一品)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">200.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">70.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">14000.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">4</td><td style="border: 1px solid #ddd; padding: 8px;">钻石(硬精品)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">50.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">50.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">2500.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">5</td><td style="border: 1px solid #ddd; padding: 8px;">双喜(硬)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">75.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">90.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">6750.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">6</td><td style="border: 1px solid #ddd; padding: 8px;">熊猫(硬)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">100.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">40.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">4000.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">7</td><td style="border: 1px solid #ddd; padding: 8px;">红塔(硬经典)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">25.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">60.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">1500.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">8</td><td style="border: 1px solid #ddd; padding: 8px;">白沙(硬)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">50.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">70.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">3500.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">9</td><td style="border: 1px solid #ddd; padding: 8px;">红双喜(硬经典)</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">525.0</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">73.20</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">38430.00</td><td style="border: 1px solid #ddd; padding: 8px; text-align: center;">/</td></tr>
                                        <tr style="background-color: #f8f9fa; font-weight: bold;">
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">合计</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;"></td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">1325.0</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;"></td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">92680.00</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;"></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div style="margin-top: 30px; font-size: 14px; line-height: 1.6;">
                                    <p><strong>价格证明依据：</strong>
                                        《国家烟草专卖局关于印发涉案卷烟产品价格管理暂行规定的通知》(国烟计〔2021〕93号)、
                                        《广东省烟草专卖局关于印发广东省烟草专卖局涉案卷烟产品价格管理规定的通知》(粤烟计〔2021〕55号)以及
                                        《广东省烟草专卖局关于发布2025年度全省涉案烟草专卖品价值评估的价格计算标准及有关事项的通知》(粤烟计〔2025〕1号)的有关规定。
                                    </p>
                                    <div style="margin-top: 40px; display: flex; justify-content: space-between;">
                                        <div>
                                            <p>经办人：<span style="margin-left: 20px;">钟伟</span></p>
                                        </div>
                                        <div style="text-align: right;">
                                            <p>当事人：<span style="margin-left: 20px;">钟伟华</span></p>
                                            <p style="margin-top: 10px;">2025年1月14日</p>
                                        </div>
                                    </div>
                                </div>
                            `,
                            hasScan: true,
                            scanUrl: 'assets/images/scan-samples/核价表扫描件.jpg'
                        },
                        { id: 19, name: '公告', title: '公告', date: '2024-01-20', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>公告内容...</p>' },
                        { id: 20, name: '案件移送函', title: '案件移送函', date: '2024-01-21', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>案件移送函内容...</p>' },
                        { id: 21, name: '移送财物清单', title: '移送财物清单', date: '2024-01-21', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>移送财物清单内容...</p>' },
                        { id: 22, name: '协助函', title: '协助函', date: '2024-01-22', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>协助函内容...</p>' },
                        { id: 23, name: '案件调查终结报告', title: '案件调查终结报告', date: '2024-01-22', status: 'normal', score: 94, issues: [], suggestions: [], content: '<p>案件调查终结报告内容...</p>' },
                        { id: 24, name: '涉案物品返还清单', title: '涉案物品返还清单', date: '2024-01-23', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>涉案物品返还清单内容...</p>' },
                        { id: 25, name: '鉴别检验留样返还清单', title: '鉴别检验留样返还清单', date: '2024-01-23', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>鉴别检验留样返还清单内容...</p>' },
                        { id: 26, name: '案件处理初审表', title: '案件处理初审表', date: '2024-01-24', status: 'normal', score: 95, issues: [], suggestions: [], content: '<p>案件处理初审表内容...</p>' },
                        { id: 27, name: '行政处罚事先告知书', title: '行政处罚事先告知书', date: '2024-01-24', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>行政处罚事先告知书内容...</p>' },
                        { id: 28, name: '陈述申辩记录', title: '陈述申辩记录', date: '2024-01-25', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>陈述申辩记录内容...</p>' },
                        { id: 29, name: '陈述申辩意见复核表', title: '陈述申辩意见复核表', date: '2024-01-25', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>陈述申辩意见复核表内容...</p>' },
                        { id: 30, name: '听证告知书', title: '听证告知书', date: '2024-01-26', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>听证告知书内容...</p>' },
                        { id: 31, name: '听证通知书', title: '听证通知书', date: '2024-01-26', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>听证通知书内容...</p>' },
                        { id: 32, name: '不予受理听证通知书', title: '不予受理听证通知书', date: '2024-01-27', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>不予受理听证通知书内容...</p>' },
                        { id: 33, name: '听证公告', title: '听证公告', date: '2024-01-27', status: 'normal', score: 85, issues: [], suggestions: [], content: '<p>听证公告内容...</p>' },
                        { id: 34, name: '听证笔录', title: '听证笔录', date: '2024-01-28', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>听证笔录内容...</p>' },
                        { id: 35, name: '听证报告', title: '听证报告', date: '2024-01-28', status: 'normal', score: 94, issues: [], suggestions: [], content: '<p>听证报告内容...</p>' },
                        { id: 36, name: '案件处理审批表', title: '案件处理审批表', date: '2024-01-29', status: 'normal', score: 96, issues: [], suggestions: [], content: '<p>案件处理审批表内容...</p>' },
                        { id: 37, name: '案件集体讨论记录', title: '案件集体讨论记录', date: '2024-01-29', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>案件集体讨论记录内容...</p>' },
                        { id: 38, name: '延长作出行政处罚决定期限审批表', title: '延长作出行政处罚决定期限审批表', date: '2024-01-30', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>延长作出行政处罚决定期限审批表内容...</p>' },
                        { id: 39, name: '延长作出行政处罚决定期限告知书', title: '延长作出行政处罚决定期限告知书', date: '2024-01-30', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>延长作出行政处罚决定期限告知书内容...</p>' },
                        { id: 40, name: '当场行政处罚决定书', title: '当场行政处罚决定书', date: '2024-01-31', status: 'normal', score: 95, issues: [], suggestions: [{ id: 2, title: '时间格式规范', description: '建议将时间格式统一为24小时制，精确到分钟', location: '笔录时间记录部分', highlightText: '10时49分', highlightStart: 0, highlightEnd: 5 }, { id: 3, title: '证据清单完善', description: '建议补充现场照片和录像证据', location: '证据材料部分', highlightText: '现场检查笔录、询问笔录、扣押物品清单', highlightStart: 0, highlightEnd: 18 }, { id: 4, title: '法条引用准确性', description: '建议核实法条条款号的准确性', location: '法律依据部分', highlightText: '第十六条第一款', highlightStart: 0, highlightEnd: 7 }, { id: 1, title: '格式建议', description: '建议在文书标题下方增加案件编号', location: '文档标题部分', highlightText: '行政处罚决定书', highlightStart: 0, highlightEnd: 7 }], content: `<h2>惠州市惠阳区烟草专卖局</h2><h3>行政处罚决定书</h3><div class="case-number">惠阳烟处（2024）第12号</div><div class="party-info"><div><span class="label">当事人：</span><span class="value">张某（男性）</span></div><div><span class="label">住址：</span><span class="value">广东省***********3号</span></div><div><span class="label">身份证：</span><span class="value">441302*******58</span></div><div><span class="label">电话：</span><span class="value">158****65</span></div><div><span class="label">经营场所：</span><span class="value">惠州市***********105（住所**）</span></div><div><span class="label">邮编：</span><span class="value">惠州市***********</span></div></div><p>2023年2月14日10时49分，我局执法人员依法对当事人经营场所进行检查，发现当事人在未取得烟草专卖零售许可证的情况下，在其经营的便利店内销售卷烟，现场查获卷烟：黄鹤楼（硬蓝）1条、黄鹤楼（软蓝）4条、黄山（红方印）3条、黄子（硬红）9条、黄子（硬蓝）3条、七匹狼（硬红）2条、七匹狼（硬蓝）3条、白沙（硬红）1条、白沙（软蓝）3条、白沙（硬蓝）1条、555（硬红）1条、黄鹤楼（硬红）1条、黄鹤楼（软红）1条、大子（硬红）1条。</p><p>上述事实，有现场检查笔录、询问笔录、扣押物品清单、身份证复印件、营业执照复印件等证据证明。</p><p>当事人的上述行为违反了《中华人民共和国烟草专卖法》第十六条第一款的规定，构成了无烟草专卖零售许可证经营烟草制品零售业务的违法行为。</p>` },
                        { id: 41, name: '行政处罚决定书', title: '行政处罚决定书', date: '2024-01-31', status: 'normal', score: 93, issues: [], suggestions: [
                            {
                                id: 1,
                                title: '违法事实与证据 (15分)',
                                description: '执法人员信息：158****65（手机）',
                                location: '执法人员信息部分',
                                highlightText: '黄国强（执法证号：19090252012）、曾玉凤（执法证号：19090252017）',
                                highlightStart: 0,
                                highlightEnd: 50,
                                status: 'pending'
                            },
                            {
                                id: 2,
                                title: '证据材料完整性',
                                description: '建议补充：1.现场检查笔录一份；2.执法人员证件照片；3.执法记录仪视频资料一份；4.当事人身份证明文件一份',
                                location: '证据材料部分',
                                highlightText: '现场当场无法提供上述卷烟的合法有效证明',
                                highlightStart: 0,
                                highlightEnd: 20,
                                status: 'pending'
                            },
                            {
                                id: 3,
                                title: '处罚依据 (10分)',
                                description: '建议补充处罚条款的具体内容和处罚金额',
                                location: '法律依据部分',
                                highlightText: '《中华人民共和国烟草专卖法实施条例》第二十三条第二款',
                                highlightStart: 0,
                                highlightEnd: 30,
                                status: 'pending'
                            }
                        ], content: `<div class="document-header"><h2>惠州市惠阳区烟草专卖局</h2><h3>行政处罚决定书</h3><div class="case-number">惠阳烟处（2025）第5号</div></div><div class="party-info"><div class="info-row"><span class="label">当事人：</span><span class="value">李某某，字号：惠州市惠阳区淡水***商行，性别：男性，民族：汉族，身份证住址：广东省惠州市大亚湾***美***2号，身份证号：44528119950219****，烟草专卖许可证号：44130319****，统一社会信用代码：92441303M****W3G，经营地址：惠州市惠阳区淡水***商行***中心A1栋2号，联系电话：1836557****</span></div></div><div class="case-content"><p><strong>案由：</strong>未在当地烟草专卖业务证据。</p><p>2025年1月4日10时30分，我局执法人员黄国强（执法证号：19090252012）、曾玉凤（执法证号：19090252017）经出示证件、表明身份，对市场作例行检查，依法对惠州市惠阳区淡水***商行***中心A1栋2号的"惠州市惠阳区淡水***商行"（烟草专卖零售许可证号：44130319****）进行检查，发现该场所有经营烟草制品零售业务的违法行为，在其经营所在地发现违法经营的卷烟有：黄山（新概念）100条、黄山（新一代）200条、双喜（硬红）25条、双喜（硬蓝）50条、红双喜（清淡型）25条共计9个品牌规格合计1325条。该涉案卷烟产品来源于上述32位当事人作供的"惠州市惠阳区淡水***商行"的专属32位当事人现场当场无法提供上述卷烟的合法有效证明。因当事人的行为涉嫌违反《中华人民共和国烟草专卖法实施条例》第二十三条第二款的规定，有未在当地烟草专卖业务违法的嫌疑，经我局研究决定，我局依法对当事人涉嫌违法上述卷烟予以先行登记保存并开具《证据先行登记保存通知书》（惠阳烟证通（2025）第5号）同日，经我局导地批准立（2025）第5号对该案进行立案调查。</strong></p></div><div class="page-number">第1页/共4页</div>` },
                        { id: 42, name: '行政处理决定书', title: '行政处理决定书', date: '2024-02-01', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>行政处理决定书内容...</p>' },
                        { id: 43, name: '不予行政处罚决定书', title: '不予行政处罚决定书', date: '2024-02-01', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>不予行政处罚决定书内容...</p>' },
                        { id: 44, name: '电子送达方式确认书', title: '电子送达方式确认书', date: '2024-02-02', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>电子送达方式确认书内容...</p>' },
                        { id: 45, name: '送达回证', title: '送达回证', date: '2024-02-02', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>送达回证内容...</p>' },
                        { id: 46, name: '送达公告', title: '送达公告', date: '2024-02-03', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>送达公告内容...</p>' },
                        { id: 47, name: '责令改正通知书', title: '责令改正通知书', date: '2024-02-03', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>责令改正通知书内容...</p>' },
                        { id: 48, name: '复查记录表', title: '复查记录表', date: '2024-02-04', status: 'normal', score: 94, issues: [], suggestions: [], content: '<p>复查记录表内容...</p>' },
                        { id: 49, name: '整顿终结通知书', title: '整顿终结通知书', date: '2024-02-04', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>整顿终结通知书内容...</p>' },
                        { id: 50, name: '销毁记录表', title: '销毁记录表', date: '2024-02-05', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>销毁记录表内容...</p>' },
                        { id: 51, name: '烟草专卖品变价处理审批表', title: '烟草专卖品变价处理审批表', date: '2024-02-05', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>烟草专卖品变价处理审批表内容...</p>' },
                        { id: 52, name: '烟草专卖品移交单', title: '烟草专卖品移交单', date: '2024-02-06', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>烟草专卖品移交单内容...</p>' },
                        { id: 53, name: '行政强制执行事项审批表', title: '行政强制执行事项审批表', date: '2024-02-06', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>行政强制执行事项审批表内容...</p>' },
                        { id: 54, name: '加处罚款决定书', title: '加处罚款决定书', date: '2024-02-07', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>加处罚款决定书内容...</p>' },
                        { id: 55, name: '行政处罚决定履行催告书', title: '行政处罚决定履行催告书', date: '2024-02-07', status: 'normal', score: 87, issues: [], suggestions: [], content: '<p>行政处罚决定履行催告书内容...</p>' },
                        { id: 56, name: '行政处罚强制执行申请书', title: '行政处罚强制执行申请书', date: '2024-02-08', status: 'normal', score: 91, issues: [], suggestions: [], content: '<p>行政处罚强制执行申请书内容...</p>' },
                        { id: 57, name: '延期（分期）缴纳罚款审批表', title: '延期（分期）缴纳罚款审批表', date: '2024-02-08', status: 'normal', score: 93, issues: [], suggestions: [], content: '<p>延期（分期）缴纳罚款审批表内容...</p>' },
                        { id: 58, name: '延期（分期）缴纳罚款通知书', title: '延期（分期）缴纳罚款通知书', date: '2024-02-09', status: 'normal', score: 88, issues: [], suggestions: [], content: '<p>延期（分期）缴纳罚款通知书内容...</p>' },
                        { id: 59, name: '对协助办案有功个人、单位授奖呈报表', title: '对协助办案有功个人、单位授奖呈报表', date: '2024-02-09', status: 'normal', score: 90, issues: [], suggestions: [], content: '<p>对协助办案有功个人、单位授奖呈报表内容...</p>' },
                        { id: 60, name: '撤销立案报告表', title: '撤销立案报告表', date: '2024-02-10', status: 'normal', score: 92, issues: [], suggestions: [], content: '<p>撤销立案报告表内容...</p>' },
                        { id: 61, name: '撤销立案通知书', title: '撤销立案通知书', date: '2024-02-10', status: 'normal', score: 89, issues: [], suggestions: [], content: '<p>撤销立案通知书内容...</p>' },
                        { id: 62, name: '结案报告表', title: '结案报告表', date: '2024-02-11', status: 'normal', score: 95, issues: [], suggestions: [], content: '<p>结案报告表内容...</p>' },
                        { id: 63, name: '卷宗封面', title: '卷宗封面', date: '2024-02-11', status: 'normal', score: 97, issues: [], suggestions: [], content: '<p>卷宗封面内容...</p>' },
                        { id: 64, name: '卷宗目录', title: '卷宗目录', date: '2024-02-12', status: 'normal', score: 96, issues: [], suggestions: [], content: '<p>卷宗目录内容...</p>' },
                        { id: 65, name: '卷内备考表', title: '卷内备考表', date: '2024-02-12', status: 'normal', score: 94, issues: [], suggestions: [], content: '<p>卷内备考表内容...</p>' }
                    ]
                };
            },
            mounted() {
                // 默认选择行政处罚决定书
                this.currentFile = this.fileList.find(file => file.id === 41) || this.fileList[0];
            },
            methods: {

                
                selectFile(file) {
                    this.currentFile = file;
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'normal': '正常',
                        'warning': '警告',
                        'error': '错误'
                    };
                    return statusMap[status] || '未知';
                },
                
                getScoreClass(score) {
                    if (score >= 90) return 'normal';
                    if (score >= 80) return 'warning';
                    return 'error';
                },
                
                previewScan(file) {
                    // 显示扫描件预览对话框
                    this.showScanPreview = true;
                    this.currentScanFile = file;
                },
                
                closeScanPreview() {
                    this.showScanPreview = false;
                    this.currentScanFile = null;
                },
                
                downloadScan() {
                    if (this.currentScanFile && this.currentScanFile.scanUrl) {
                        // 创建一个临时链接来下载图片
                        const link = document.createElement('a');
                        link.href = this.currentScanFile.scanUrl;
                        link.download = `${this.currentScanFile.name}_扫描件.jpg`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        ElMessage.success('扫描件下载已开始');
                    }
                },
                
                handleImageError(event) {
                    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCA4MDAgMTAwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwMCIgaGVpZ2h0PSIxMDAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MDAgNTAwTDQ1MCA0NTBINTUwVjU1MEg0NTBMNDAwIDUwMFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHRleHQgeD0iNDAwIiB5PSI2MDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5omr5o+P5Lu25peg5rOV5Yqg6L29PC90ZXh0Pgo8L3N2Zz4K';
                    ElMessage.warning('扫描件加载失败，请稍后重试');
                },
                
                highlightSuggestion(suggestion) {
                    // 设置当前活跃的建议
                    this.activeSuggestion = suggestion.id;
                    
                    // 清除之前的高亮
                    this.clearHighlights();
                    
                    // 高亮文档中对应的文字
                    if (suggestion.highlightText) {
                        this.highlightTextInDocument(suggestion.highlightText);
                    }
                    
                    // 滚动到文档内容区域
                    this.$nextTick(() => {
                        const contentArea = document.querySelector('.content-body');
                        if (contentArea) {
                            contentArea.scrollTop = 0;
                        }
                    });
                },
                
                highlightTextInDocument(text) {
                    const contentArea = document.querySelector('.document-content');
                    if (!contentArea) return;
                    
                    const walker = document.createTreeWalker(
                        contentArea,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );
                    
                    const textNodes = [];
                    let node;
                    while (node = walker.nextNode()) {
                        textNodes.push(node);
                    }
                    
                    textNodes.forEach(textNode => {
                        const content = textNode.textContent;
                        const index = content.indexOf(text);
                        if (index !== -1) {
                            const parent = textNode.parentNode;
                            const beforeText = content.substring(0, index);
                            const highlightText = content.substring(index, index + text.length);
                            const afterText = content.substring(index + text.length);
                            
                            const fragment = document.createDocumentFragment();
                            
                            if (beforeText) {
                                fragment.appendChild(document.createTextNode(beforeText));
                            }
                            
                            const highlightSpan = document.createElement('span');
                            highlightSpan.className = 'highlight';
                            highlightSpan.textContent = highlightText;
                            fragment.appendChild(highlightSpan);
                            
                            if (afterText) {
                                fragment.appendChild(document.createTextNode(afterText));
                            }
                            
                            parent.replaceChild(fragment, textNode);
                        }
                    });
                },
                
                clearHighlights() {
                    const highlights = document.querySelectorAll('.highlight');
                    highlights.forEach(highlight => {
                        const parent = highlight.parentNode;
                        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                        parent.normalize();
                    });
                },
                
                acceptSuggestion(suggestion) {
                    ElMessage.success(`已采纳建议：${suggestion.title}`);
                    // 这里可以添加采纳建议的具体逻辑
                    console.log('采纳建议:', suggestion);
                },
                
                ignoreSuggestion(suggestion) {
                    ElMessage.info(`已忽略建议：${suggestion.title}`);
                    // 这里可以添加忽略建议的具体逻辑
                    console.log('忽略建议:', suggestion);
                },
                
                exportCaseFiles() {
                    // 显示导出进度
                    const loading = ElLoading.service({
                        lock: true,
                        text: '正在导出案卷文件...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    
                    try {
                        // 创建ZIP文件内容
                        const caseData = {
                            caseInfo: this.currentCase,
                            files: this.fileList.map(file => ({
                                id: file.id,
                                name: file.name,
                                title: file.title,
                                content: file.content,
                                status: file.status,
                                score: file.score,
                                issues: file.issues,
                                suggestions: file.suggestions,
                                date: file.date
                            })),
                            exportTime: new Date().toLocaleString('zh-CN'),
                            exportBy: this.currentCase.officer
                        };
                        
                        // 生成案卷报告HTML
                        const reportHtml = this.generateCaseReport(caseData);
                        
                        // 创建下载链接
                        const blob = new Blob([reportHtml], { type: 'text/html;charset=utf-8' });
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `${this.currentCase.name}_案卷文件_${new Date().toISOString().slice(0, 10)}.html`;
                        
                        // 触发下载
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        URL.revokeObjectURL(url);
                        
                        // 同时导出JSON格式的数据
                        const jsonBlob = new Blob([JSON.stringify(caseData, null, 2)], { type: 'application/json' });
                        const jsonUrl = URL.createObjectURL(jsonBlob);
                        const jsonLink = document.createElement('a');
                        jsonLink.href = jsonUrl;
                        jsonLink.download = `${this.currentCase.name}_案卷数据_${new Date().toISOString().slice(0, 10)}.json`;
                        
                        document.body.appendChild(jsonLink);
                        jsonLink.click();
                        document.body.removeChild(jsonLink);
                        URL.revokeObjectURL(jsonUrl);
                        
                        loading.close();
                        ElMessage.success('案卷文件导出成功！');
                        
                    } catch (error) {
                        loading.close();
                        ElMessage.error('导出失败，请重试');
                        console.error('导出错误:', error);
                    }
                },
                
                generateCaseReport(caseData) {
                    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${caseData.caseInfo.name} - 案卷报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 40px; line-height: 1.6; color: #333; }
        .header { text-align: center; border-bottom: 3px solid #409eff; padding-bottom: 20px; margin-bottom: 30px; }
        .case-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .file-section { margin-bottom: 40px; }
        .file-title { background: #409eff; color: white; padding: 10px 15px; margin: 0; font-size: 18px; }
        .file-content { border: 1px solid #ddd; padding: 20px; margin-bottom: 20px; }
        .status-normal { color: #67c23a; }
        .status-warning { color: #e6a23c; }
        .status-error { color: #f56c6c; }
        .issues { background: #fef0f0; border-left: 4px solid #f56c6c; padding: 15px; margin: 10px 0; }
        .suggestions { background: #fdf6ec; border-left: 4px solid #e6a23c; padding: 15px; margin: 10px 0; }
        .export-info { text-align: center; color: #909399; font-size: 12px; margin-top: 40px; border-top: 1px solid #eee; padding-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${caseData.caseInfo.name} - 案卷报告</h1>
        <p>专卖案件文书大模型智办系统</p>
    </div>
    
    <div class="case-info">
        <h2>案件基本信息</h2>
        <p><strong>案件名称：</strong>${caseData.caseInfo.name}</p>
        <p><strong>立案时间：</strong>${caseData.caseInfo.date}</p>
        <p><strong>承办人：</strong>${caseData.caseInfo.officer}</p>
        <p><strong>当事人：</strong>${caseData.caseInfo.party}</p>
    </div>
    
    ${caseData.files.map(file => `
    <div class="file-section">
        <h3 class="file-title">${file.name} - ${file.title}</h3>
        <div class="file-content">
            <p><strong>状态：</strong><span class="status-${file.status}">${this.getStatusText(file.status)}</span></p>
            <p><strong>评查得分：</strong>${file.score}分</p>
            <p><strong>文件日期：</strong>${file.date}</p>
            
            ${file.issues.length > 0 ? `
            <div class="issues">
                <h4>发现的问题：</h4>
                ${file.issues.map(issue => `<p>• ${issue.title}: ${issue.description}</p>`).join('')}
            </div>
            ` : ''}
            
            ${file.suggestions.length > 0 ? `
            <div class="suggestions">
                <h4>修改建议：</h4>
                ${file.suggestions.map(suggestion => `<p>• ${suggestion.title}: ${suggestion.description}</p>`).join('')}
            </div>
            ` : ''}
            
            <div style="margin-top: 20px;">
                <h4>文件内容：</h4>
                <div style="border: 1px solid #eee; padding: 15px; background: #fafafa;">
                    ${file.content}
                </div>
            </div>
        </div>
    </div>
    `).join('')}
    
    <div class="export-info">
        <p>导出时间：${caseData.exportTime} | 导出人：${caseData.exportBy}</p>
        <p>本报告由专卖案件文书大模型智办系统自动生成</p>
    </div>
</body>
</html>`;
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>