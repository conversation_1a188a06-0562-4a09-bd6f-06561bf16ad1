<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案卷查询 - 专卖案件文书大模型智办系统</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        /* 面包屑样式 */
        .breadcrumb {
            background: #ffffff;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;
            text-decoration: none;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: #409EFF;
        }

        .breadcrumb-item.current {
            color: #303133;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #C0C4CC;
            margin: 0 4px;
        }

        .breadcrumb-icon {
            font-size: 16px;
        }
        
        .query-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .query-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .query-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .query-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #EBEEF5;
        }
        
        .results-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .batch-actions {
            display: flex;
            gap: 12px;
        }
        
        .case-table {
            width: 100%;
            min-width: 1200px;
        }

        .results-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        /* 表格滚动条样式 */
        .results-container::-webkit-scrollbar {
            height: 8px;
        }

        .results-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 修复表格右侧固定列 */
        .el-table__fixed-right {
            right: 0 !important;
            z-index: 3;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .el-table__fixed-right-patch {
            display: none !important;
        }
        
        .status-tag {
            font-size: 12px;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        /* 编辑对话框样式 */
        .edit-dialog .el-dialog__body {
            padding: 20px;
        }
        
        .edit-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .edit-form .full-width {
            grid-column: 1 / -1;
        }
        
        /* 导出进度对话框 */
        .export-progress {
            text-align: center;
            padding: 20px;
        }
        
        .progress-icon {
            font-size: 48px;
            color: #409EFF;
            margin-bottom: 16px;
        }
        
        .progress-text {
            font-size: 16px;
            color: #606266;
            margin-bottom: 20px;
        }
        
        /* 上传对话框样式 */
        .upload-dialog .el-dialog__body {
            padding: 20px;
        }

        /* 对话框动画 */
        .el-dialog {
            animation: dialogFadeIn 0.3s ease-out;
        }

        @keyframes dialogFadeIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-area:hover {
            border-color: #409EFF;
            background: #f0f9ff;
        }

        .upload-area.dragover {
            border-color: #409EFF;
            background: #e6f7ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #c0c4cc;
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 16px;
            color: #606266;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: #909399;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            margin-bottom: 8px;
            background: white;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .file-icon {
            font-size: 24px;
            color: #409EFF;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
        }

        .file-size {
            font-size: 12px;
            color: #909399;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .upload-progress {
            margin-top: 12px;
        }

        .upload-case-info {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #409EFF;
        }

        .upload-case-info p {
            margin: 4px 0;
            font-size: 14px;
            color: #606266;
        }

        /* 识别结果样式 */
        .recognition-results {
            margin-top: 20px;
            padding: 16px;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #d1ecf1;
        }

        .recognition-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .recognition-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .recognition-title-right {
            display: flex;
            align-items: center;
        }

        .recognition-icon {
            color: #409EFF;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .recognition-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .recognition-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;
            gap: 12px;
        }

        .recognition-item-checkbox {
            flex-shrink: 0;
        }

        .recognition-item:hover {
            border-color: #409EFF;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .recognition-item.duplicate {
            border-color: #f56c6c;
            background: #fef0f0;
        }

        .recognition-item-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .recognition-item-icon {
            font-size: 20px;
            color: #409EFF;
        }

        .recognition-item-details {
            display: flex;
            flex-direction: column;
        }

        .recognition-item-name {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
        }

        .recognition-item-type {
            font-size: 12px;
            color: #909399;
        }

        .recognition-item-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .duplicate-warning {
            background: #fff2e8;
            border: 1px solid #f7ba2a;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .duplicate-warning-icon {
            color: #f7ba2a;
            font-size: 18px;
        }

        .duplicate-warning-text {
            font-size: 14px;
            color: #e6a23c;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .query-form {
                grid-template-columns: 1fr;
            }

            .edit-form {
                grid-template-columns: 1fr;
            }

            .results-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .batch-actions {
                justify-content: center;
            }

            .action-buttons {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 页面头部 -->
        <div class="header">
            <h1>案卷查询管理</h1>
            <p>查询案卷档案 · 编辑未归档案卷 · 批量导出文档</p>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html" class="breadcrumb-item">
                    <i class="breadcrumb-icon">🏠</i>
                    <span>首页</span>
                </a>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item current">
                    <i class="breadcrumb-icon">📁</i>
                    <span>案卷查询</span>
                </span>
            </div>
            
            <!-- 查询条件区域 -->
            <div class="query-container">
                <div class="query-title">
                    <el-icon><Search /></el-icon>
                    查询条件
                </div>
                
                <div class="query-form">
                    <el-form-item label="案件编号">
                        <el-input 
                            v-model="queryForm.caseNumber" 
                            placeholder="请输入案件编号"
                            clearable>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="当事人姓名">
                        <el-input 
                            v-model="queryForm.partyName" 
                            placeholder="请输入当事人姓名"
                            clearable>
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item label="案件状态">
                        <el-select 
                            v-model="queryForm.status" 
                            placeholder="请选择案件状态"
                            clearable>
                            <el-option label="全部" value=""></el-option>
                            <el-option label="未归档" value="pending"></el-option>
                            <el-option label="已归档" value="archived"></el-option>
                            <el-option label="已结案" value="closed"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="案件类型">
                        <el-select 
                            v-model="queryForm.caseType" 
                            placeholder="请选择案件类型"
                            clearable>
                            <el-option label="全部" value=""></el-option>
                            <el-option label="无证经营" value="unlicensed"></el-option>
                            <el-option label="超范围经营" value="beyond_scope"></el-option>
                            <el-option label="销售假烟" value="fake_cigarettes"></el-option>
                            <el-option label="其他违法" value="other"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="创建时间">
                        <el-date-picker
                            v-model="queryForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD">
                        </el-date-picker>
                    </el-form-item>
                    
                    <el-form-item label="办案人员">
                        <el-input 
                            v-model="queryForm.officer" 
                            placeholder="请输入办案人员姓名"
                            clearable>
                        </el-input>
                    </el-form-item>
                </div>
                
                <div class="query-actions">
                    <el-button @click="resetQuery">
                        <el-icon><Refresh /></el-icon>
                        重置
                    </el-button>
                    <el-button type="primary" @click="searchCases">
                        <el-icon><Search /></el-icon>
                        查询
                    </el-button>
                </div>
            </div>
            
            <!-- 查询结果区域 -->
            <div class="results-container">
                <div class="results-header">
                    <div class="results-title">
                        <el-icon><List /></el-icon>
                        查询结果 (共 {{ total }} 条)
                    </div>
                    
                    <div class="batch-actions">
                        <el-button 
                            type="success" 
                            :disabled="selectedCases.length === 0"
                            @click="batchExport">
                            <el-icon><Download /></el-icon>
                            批量导出 ({{ selectedCases.length }})
                        </el-button>
                        <el-button 
                            type="warning" 
                            :disabled="selectedPendingCases.length === 0"
                            @click="batchEdit">
                            <el-icon><Edit /></el-icon>
                            批量编辑 ({{ selectedPendingCases.length }})
                        </el-button>
                    </div>
                </div>
                
                <el-table 
                    :data="caseList" 
                    class="case-table"
                    @selection-change="handleSelectionChange"
                    v-loading="loading">
                    
                    <el-table-column type="selection" width="55"></el-table-column>
                    
                    <el-table-column prop="caseNumber" label="案件编号" width="260">
                        <template #default="scope">
                            <el-link type="primary" @click="viewCase(scope.row)">
                                {{ scope.row.caseNumber }}
                            </el-link>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="partyName" label="当事人" width="100"></el-table-column>
                    
                    <el-table-column prop="caseType" label="案件类型" width="110">
                        <template #default="scope">
                            {{ getCaseTypeText(scope.row.caseType) }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="status" label="状态" width="90">
                        <template #default="scope">
                            <el-tag 
                                :type="getStatusType(scope.row.status)" 
                                class="status-tag">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="officer" label="办案人员" width="100"></el-table-column>
                    
                    <el-table-column prop="createTime" label="创建时间" width="110"></el-table-column>
                    
                    <el-table-column prop="updateTime" label="更新时间" width="110"></el-table-column>
                    
                    <el-table-column label="操作"  fixed="right">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button size="small" @click=" viewExistingFile(scope.row)">
                                    <!-- <el-icon><View /></el-icon> -->
                                    查看文书
                                </el-button>
                                <el-tooltip 
                                    :content="'仅支持对未归档的案件进行案卷编辑'"
                                    placement="top">
                                    <el-button 
                                        size="small" 
                                        type="warning"
                                        :disabled="scope.row.status === 'archived'"
                                        @click="generateCase(scope.row)">
                                        案卷生成
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip 
                                    :content="'仅支持对已结案的案件进行案卷评查'"
                                    placement="top">
                                    <el-button 
                                        size="small" 
                                        type="warning"
                                        :disabled="scope.row.status !== 'closed'"
                                        @click="reviewCase(scope.row)">
                                        案卷评查
                                    </el-button>
                                </el-tooltip>
                                
                                <el-button
                                    size="small"
                                    type="primary"
                                    :disabled="scope.row.status === 'archived'"
                                    @click="uploadScanFiles(scope.row)">
                                    <!-- <el-icon><Upload /></el-icon> -->
                                    上传扫描件
                                </el-button>

                                <el-button
                                    size="small"
                                    type="success"
                                    @click="exportCase(scope.row)">
                                    <el-icon><Download /></el-icon>
                                    导出
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange">
                    </el-pagination>
                </div>
            </div>
        </div>
        
        <!-- 编辑案件对话框 -->
        <el-dialog 
            v-model="editDialogVisible" 
            title="编辑案件信息" 
            width="800px"
            class="edit-dialog">
            
            <el-form :model="editForm" label-width="120px">
                <div class="edit-form">
                    <el-form-item label="案件编号">
                        <el-input v-model="editForm.caseNumber" disabled></el-input>
                    </el-form-item>
                    
                    <el-form-item label="当事人姓名">
                        <el-input v-model="editForm.partyName"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="案件类型">
                        <el-select v-model="editForm.caseType">
                            <el-option label="无证经营" value="unlicensed"></el-option>
                            <el-option label="超范围经营" value="beyond_scope"></el-option>
                            <el-option label="销售假烟" value="fake_cigarettes"></el-option>
                            <el-option label="其他违法" value="other"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="办案人员">
                        <el-input v-model="editForm.officer"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="案件描述" class="full-width">
                        <el-input 
                            v-model="editForm.description" 
                            type="textarea" 
                            :rows="4"
                            placeholder="请输入案件描述">
                        </el-input>
                    </el-form-item>
                </div>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveEdit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        
        <!-- 扫描件上传对话框 -->
        <el-dialog
            v-model="uploadDialogVisible"
            title="上传扫描件"
            width="600px"
            class="upload-dialog">

            <div class="upload-case-info">
                <p><strong>案件编号：</strong>{{ currentCase.caseNumber }}</p>
                <p><strong>当事人：</strong>{{ currentCase.partyName }}</p>
            </div>

            <div class="upload-area"
                 @click="triggerFileInput"
                 @dragover.prevent="handleDragOver"
                 @dragleave.prevent="handleDragLeave"
                 @drop.prevent="handleDrop"
                 :class="{ dragover: isDragOver }">
                <div class="upload-icon">
                    <el-icon><UploadFilled /></el-icon>
                </div>
                <div class="upload-text">点击或拖拽文件到此区域上传</div>
                <div class="upload-hint">支持 PDF、JPG、PNG、JPEG 格式，单个文件不超过 10MB</div>
            </div>

            <input
                ref="fileInput"
                type="file"
                multiple
                accept=".pdf,.jpg,.jpeg,.png"
                style="display: none"
                @change="handleFileSelect">

            <div class="file-list" v-if="uploadFiles.length > 0">
                <h4>已选择文件：</h4>
                <div class="file-item" v-for="(file, index) in uploadFiles" :key="index">
                    <div class="file-info">
                        <div class="file-icon">
                            <el-icon v-if="file.type.includes('pdf')"><Document /></el-icon>
                            <el-icon v-else><Picture /></el-icon>
                        </div>
                        <div class="file-details">
                            <div class="file-name">{{ file.name }}</div>
                            <div class="file-size">{{ formatFileSize(file.size) }}</div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <el-button size="small" type="danger" @click="removeFile(index)">
                            <el-icon><Delete /></el-icon>
                        </el-button>
                    </div>
                </div>
            </div>

            <div class="upload-progress" v-if="isUploading">
                <el-progress
                    :percentage="uploadProgress"
                    :stroke-width="6"
                    color="#409EFF">
                </el-progress>
                <p style="text-align: center; margin-top: 8px; color: #606266;">{{ uploadProgressText }}</p>
            </div>

            <!-- 识别结果区域 -->
            <div class="recognition-results" v-if="recognitionResults.length > 0">
                <div class="recognition-title">
                    <div class="recognition-title-left">
                        <el-icon class="recognition-icon" v-if="isRecognizing"><Loading /></el-icon>
                        <el-icon v-else><DocumentChecked /></el-icon>
                        {{ isRecognizing ? '正在识别扫描件内容...' : '识别到以下文件' }}
                    </div>
                    <div class="recognition-title-right" v-if="!isRecognizing">
                        <el-button type="text" size="small" @click="selectAllFiles">
                            {{ allFilesSelected ? '取消全选' : '全选' }}
                        </el-button>
                    </div>
                </div>

                <ul class="recognition-list">
                    <li class="recognition-item"
                        v-for="(item, index) in recognitionResults"
                        :key="index"
                        :class="{ duplicate: item.isDuplicate }">
                        <div class="recognition-item-checkbox">
                            <el-checkbox v-model="item.selected" @change="onItemSelectionChange"></el-checkbox>
                        </div>
                        <div class="recognition-item-info">
                            <div class="recognition-item-icon">
                                <el-icon v-if="item.type === 'document'"><Document /></el-icon>
                                <el-icon v-else-if="item.type === 'form'"><Tickets /></el-icon>
                                <el-icon v-else-if="item.type === 'certificate'"><Medal /></el-icon>
                                <el-icon v-else><Files /></el-icon>
                            </div>
                            <div class="recognition-item-details">
                                <div class="recognition-item-name">{{ item.name }}</div>
                                <div class="recognition-item-type">{{ item.typeText }}</div>
                            </div>
                        </div>
                        <div class="recognition-item-actions">
                            <el-tag v-if="item.isDuplicate" type="warning" size="small">已存在</el-tag>
                            <el-button v-if="item.isDuplicate" 
                                      type="text" 
                                      size="small" 
                                      @click="viewExistingFile(item)">
                                <!-- <el-icon><View /></el-icon> -->
                                查看文书
                            </el-button>
                        </div>
                    </li>
                </ul>

                <!-- 重复文件警告 -->
                <div class="duplicate-warning" v-if="hasDuplicateFiles">
                    <el-icon class="duplicate-warning-icon"><Warning /></el-icon>
                    <div class="duplicate-warning-text">
                        检测到 {{ duplicateCount }} 个文件已存在，上传将覆盖原有文件
                    </div>
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelUpload">取消</el-button>
                    <el-button
                        type="primary"
                        @click="startUpload"
                        :disabled="uploadFiles.length === 0 || isUploading || isRecognizing || recognitionResults.filter(item => item.selected).length === 0">
                        {{ getUploadButtonText() }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 导出进度对话框 -->
        <el-dialog
            v-model="exportDialogVisible"
            title="导出进度"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false">

            <div class="export-progress">
                <div class="progress-icon">
                    <el-icon><Loading /></el-icon>
                </div>
                <div class="progress-text">{{ exportProgressText }}</div>
                <el-progress
                    :percentage="exportProgress"
                    :stroke-width="8"
                    color="#409EFF">
                </el-progress>
            </div>
        </el-dialog>
    </div>
    
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        createApp({
            data() {
                return {
                    // 查询表单
                    queryForm: {
                        caseNumber: '',
                        partyName: '',
                        status: '',
                        caseType: '',
                        dateRange: [],
                        officer: ''
                    },
                    
                    // 案件列表
                    caseList: [],
                    total: 0,
                    currentPage: 1,
                    pageSize: 20,
                    loading: false,
                    
                    // 选中的案件
                    selectedCases: [],
                    
                    // 编辑对话框
                    editDialogVisible: false,
                    editForm: {
                        id: '',
                        caseNumber: '',
                        partyName: '',
                        caseType: '',
                        officer: '',
                        description: ''
                    },
                    
                    // 导出进度
                    exportDialogVisible: false,
                    exportProgress: 0,
                    exportProgressText: '正在准备导出...',

                    // 上传相关
                    uploadDialogVisible: false,
                    currentCase: {},
                    uploadFiles: [],
                    isDragOver: false,
                    isUploading: false,
                    uploadProgress: 0,
                    uploadProgressText: '',

                    // 识别相关
                    isRecognizing: false,
                    recognitionResults: [],
                    existingFiles: [] // 模拟已存在的文件
                };
            },
            
            computed: {
                selectedPendingCases() {
                    return this.selectedCases.filter(item => item.status === 'pending');
                },

                hasDuplicateFiles() {
                    return this.recognitionResults.some(item => item.isDuplicate);
                },

                duplicateCount() {
                    return this.recognitionResults.filter(item => item.isDuplicate).length;
                },

                allFilesSelected() {
                    return this.recognitionResults.length > 0 && this.recognitionResults.every(item => item.selected);
                }
            },
            
            mounted() {
                this.loadMockData();
                this.searchCases();
            },
            
            methods: {
                // 加载模拟数据
                loadMockData() {
                    const mockCases = [];
                    const caseTypes = ['unlicensed', 'beyond_scope', 'fake_cigarettes', 'other'];
                    const statuses = ['pending', 'archived', 'closed'];
                    const officers = ['张三', '李四', '王五', '赵六', '钱七'];
                    const partyNames = ['陈某', '刘某', '黄某', '林某', '郑某', '吴某', '周某', '徐某'];
                    
                    for (let i = 1; i <= 50; i++) {
                        const createDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
                        const updateDate = new Date(createDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
                        
                        mockCases.push({
                            id: i,
                            caseNumber: `惠阳烟处（2024）第${String(i).padStart(3, '0')}号`,
                            partyName: partyNames[Math.floor(Math.random() * partyNames.length)],
                            caseType: caseTypes[Math.floor(Math.random() * caseTypes.length)],
                            status: statuses[Math.floor(Math.random() * statuses.length)],
                            officer: officers[Math.floor(Math.random() * officers.length)],
                            createTime: createDate.toISOString().split('T')[0],
                            updateTime: updateDate.toISOString().split('T')[0],
                            description: `案件${i}的详细描述信息...`
                        });
                    }
                    
                    this.allCases = mockCases;
                },
                
                // 查询案件
                searchCases() {
                    this.loading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        let filteredCases = [...this.allCases];
                        
                        // 应用筛选条件
                        if (this.queryForm.caseNumber) {
                            filteredCases = filteredCases.filter(item => 
                                item.caseNumber.includes(this.queryForm.caseNumber)
                            );
                        }
                        
                        if (this.queryForm.partyName) {
                            filteredCases = filteredCases.filter(item => 
                                item.partyName.includes(this.queryForm.partyName)
                            );
                        }
                        
                        if (this.queryForm.status) {
                            filteredCases = filteredCases.filter(item => 
                                item.status === this.queryForm.status
                            );
                        }
                        
                        if (this.queryForm.caseType) {
                            filteredCases = filteredCases.filter(item => 
                                item.caseType === this.queryForm.caseType
                            );
                        }
                        
                        if (this.queryForm.officer) {
                            filteredCases = filteredCases.filter(item => 
                                item.officer.includes(this.queryForm.officer)
                            );
                        }
                        
                        if (this.queryForm.dateRange && this.queryForm.dateRange.length === 2) {
                            const [startDate, endDate] = this.queryForm.dateRange;
                            filteredCases = filteredCases.filter(item => 
                                item.createTime >= startDate && item.createTime <= endDate
                            );
                        }
                        
                        this.total = filteredCases.length;
                        
                        // 分页
                        const start = (this.currentPage - 1) * this.pageSize;
                        const end = start + this.pageSize;
                        this.caseList = filteredCases.slice(start, end);
                        
                        this.loading = false;
                    }, 500);
                },
                
                // 重置查询
                resetQuery() {
                    this.queryForm = {
                        caseNumber: '',
                        partyName: '',
                        status: '',
                        caseType: '',
                        dateRange: [],
                        officer: ''
                    };
                    this.currentPage = 1;
                    this.searchCases();
                },
                
                // 处理选择变化
                handleSelectionChange(selection) {
                    this.selectedCases = selection;
                },
                
                // 查看案件
                viewCase(row) {
                    ElMessage({
                        message: `正在查看案件：${row.caseNumber}`,
                        type: 'info'
                    });
                    // 这里可以跳转到案件详情页面
                },
                
                // 编辑案件
                editCase(row) {
                    this.editForm = {
                        id: row.id,
                        caseNumber: row.caseNumber,
                        partyName: row.partyName,
                        caseType: row.caseType,
                        officer: row.officer,
                        description: row.description
                    };
                    this.editDialogVisible = true;
                },
                
                // 案卷生成
                generateCase(row) {
                    // 将案件信息存储到localStorage中，供目标页面使用
                    localStorage.setItem('selectedCase', JSON.stringify(row));
                    // 跳转到案卷生成页面
                    window.location.href = 'case-generation.html';
                },
                
                // 案卷评查
                reviewCase(row) {
                    // 将案件信息存储到localStorage中，供目标页面使用
                    localStorage.setItem('selectedCase', JSON.stringify(row));
                    // 跳转到案卷评查页面
                    window.location.href = 'case-review.html';
                },
                
                // 保存编辑
                saveEdit() {
                    // 模拟保存
                    const index = this.caseList.findIndex(item => item.id === this.editForm.id);
                    if (index !== -1) {
                        Object.assign(this.caseList[index], this.editForm);
                        this.caseList[index].updateTime = new Date().toISOString().split('T')[0];
                    }
                    
                    ElMessage({
                        message: '案件信息保存成功',
                        type: 'success'
                    });
                    
                    this.editDialogVisible = false;
                },
                
                // 导出单个案件
                exportCase(row) {
                    this.startExport([row]);
                },
                
                // 批量导出
                batchExport() {
                    if (this.selectedCases.length === 0) {
                        ElMessage({
                            message: '请先选择要导出的案件',
                            type: 'warning'
                        });
                        return;
                    }
                    
                    this.startExport(this.selectedCases);
                },
                
                // 批量编辑
                batchEdit() {
                    if (this.selectedPendingCases.length === 0) {
                        ElMessage({
                            message: '请先选择未归档的案件进行编辑',
                            type: 'warning'
                        });
                        return;
                    }
                    
                    ElMessage({
                        message: `正在批量编辑 ${this.selectedPendingCases.length} 个案件`,
                        type: 'info'
                    });
                },
                
                // 开始导出
                startExport(cases) {
                    this.exportDialogVisible = true;
                    this.exportProgress = 0;
                    this.exportProgressText = '正在准备导出...';
                    
                    // 模拟导出进度
                    const interval = setInterval(() => {
                        this.exportProgress += Math.random() * 20;
                        
                        if (this.exportProgress < 30) {
                            this.exportProgressText = '正在生成Word文档...';
                        } else if (this.exportProgress < 60) {
                            this.exportProgressText = '正在处理文档格式...';
                        } else if (this.exportProgress < 90) {
                            this.exportProgressText = '正在压缩文件...';
                        } else {
                            this.exportProgressText = '即将完成...';
                        }
                        
                        if (this.exportProgress >= 100) {
                            this.exportProgress = 100;
                            this.exportProgressText = '导出完成！';
                            clearInterval(interval);
                            
                            setTimeout(() => {
                                this.exportDialogVisible = false;
                                ElMessage({
                                    message: `成功导出 ${cases.length} 个案件的Word文档`,
                                    type: 'success'
                                });
                            }, 1000);
                        }
                    }, 200);
                },
                
                // 分页处理
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.searchCases();
                },
                
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.searchCases();
                },
                
                // 获取案件类型文本
                getCaseTypeText(type) {
                    const typeMap = {
                        'unlicensed': '无证经营',
                        'beyond_scope': '超范围经营',
                        'fake_cigarettes': '销售假烟',
                        'other': '其他违法'
                    };
                    return typeMap[type] || type;
                },
                
                // 获取状态文本
                getStatusText(status) {
                    const statusMap = {
                        'pending': '未归档',
                        'archived': '已归档',
                        'closed': '已结案'
                    };
                    return statusMap[status] || status;
                },
                
                // 获取状态类型
                getStatusType(status) {
                    const typeMap = {
                        'pending': 'warning',
                        'archived': 'success',
                        'closed': 'info'
                    };
                    return typeMap[status] || 'info';
                },

                // 上传扫描件
                uploadScanFiles(row) {
                    this.currentCase = row;
                    this.uploadFiles = [];
                    this.recognitionResults = [];
                    this.isRecognizing = false;

                    // 模拟该案件已存在的文件
                    this.existingFiles = [
                        '现场检查笔录',
                        '调查询问笔录',
                        '身份证复印件',
                        '营业执照复印件'
                    ];

                    this.uploadDialogVisible = true;
                },

                // 触发文件选择
                triggerFileInput() {
                    this.$refs.fileInput.click();
                },

                // 处理文件选择
                handleFileSelect(event) {
                    const files = Array.from(event.target.files);
                    this.addFiles(files);
                    // 清空input值，允许重复选择同一文件
                    event.target.value = '';
                },

                // 处理拖拽
                handleDragOver(event) {
                    this.isDragOver = true;
                },

                handleDragLeave(event) {
                    this.isDragOver = false;
                },

                handleDrop(event) {
                    this.isDragOver = false;
                    const files = Array.from(event.dataTransfer.files);
                    this.addFiles(files);
                },

                // 添加文件
                addFiles(files) {
                    const validFiles = files.filter(file => {
                        // 检查文件类型
                        const validTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                        if (!validTypes.includes(file.type)) {
                            ElMessage({
                                message: `文件 "${file.name}" 格式不支持，请选择 PDF、JPG、PNG 格式的文件`,
                                type: 'warning'
                            });
                            return false;
                        }

                        // 检查文件大小（10MB）
                        if (file.size > 10 * 1024 * 1024) {
                            ElMessage({
                                message: `文件 "${file.name}" 大小超过 10MB，请选择较小的文件`,
                                type: 'warning'
                            });
                            return false;
                        }

                        return true;
                    });

                    this.uploadFiles.push(...validFiles);

                    if (validFiles.length > 0) {
                        ElMessage({
                            message: `成功添加 ${validFiles.length} 个文件`,
                            type: 'success'
                        });

                        // 开始识别文件内容
                        this.startRecognition();
                    }
                },

                // 移除文件
                removeFile(index) {
                    this.uploadFiles.splice(index, 1);
                },

                // 格式化文件大小
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },

                // 开始上传
                async startUpload() {
                    if (this.uploadFiles.length === 0) {
                        ElMessage({
                            message: '请先选择要上传的文件',
                            type: 'warning'
                        });
                        return;
                    }

                    // 检查是否有选中的文件
                    const selectedFiles = this.recognitionResults.filter(item => item.selected);
                    if (selectedFiles.length === 0) {
                        ElMessage({
                            message: '请至少选择一个文件进行上传',
                            type: 'warning'
                        });
                        return;
                    }

                    // 检查选中的文件中是否有重复文件
                    const selectedDuplicateFiles = selectedFiles.filter(item => item.isDuplicate);
                    if (selectedDuplicateFiles.length > 0) {
                        const duplicateNames = selectedDuplicateFiles.map(item => item.name).join('、');
                        try {
                            await ElMessageBox.confirm(
                                `以下文件已存在，上传将覆盖原有文件：\n\n${duplicateNames}\n\n是否继续上传？`,
                                '确认覆盖文件',
                                {
                                    confirmButtonText: '确认覆盖',
                                    cancelButtonText: '取消上传',
                                    type: 'warning',
                                    dangerouslyUseHTMLString: true
                                }
                            );
                        } catch {
                            return; // 用户取消
                        }
                    }

                    this.isUploading = true;
                    this.uploadProgress = 0;
                    this.uploadProgressText = '正在上传文件...';

                    // 模拟上传进度
                    const interval = setInterval(() => {
                        this.uploadProgress += Math.random() * 15;

                        if (this.uploadProgress < 30) {
                            this.uploadProgressText = '正在上传文件...';
                        } else if (this.uploadProgress < 60) {
                            this.uploadProgressText = '正在处理文件...';
                        } else if (this.uploadProgress < 90) {
                            this.uploadProgressText = '正在保存到服务器...';
                        } else {
                            this.uploadProgressText = '即将完成...';
                        }

                        if (this.uploadProgress >= 100) {
                            this.uploadProgress = 100;
                            this.uploadProgressText = '上传完成！';
                            clearInterval(interval);

                            setTimeout(() => {
                                this.isUploading = false;
                                this.uploadDialogVisible = false;

                                const selectedFiles = this.recognitionResults.filter(item => item.selected);
                                const uploadedFiles = selectedFiles.map(r => r.name).join('、');
                                ElMessage({
                                    message: `成功上传 ${selectedFiles.length} 个扫描件到案件 ${this.currentCase.caseNumber}：${uploadedFiles}`,
                                    type: 'success',
                                    duration: 5000
                                });

                                this.uploadFiles = [];
                                this.recognitionResults = [];
                            }, 1000);
                        }
                    }, 200);
                },

                // 取消上传
                cancelUpload() {
                    this.uploadDialogVisible = false;
                    this.uploadFiles = [];
                    this.isUploading = false;
                    this.uploadProgress = 0;
                    this.recognitionResults = [];
                    this.isRecognizing = false;
                },

                // 开始识别
                startRecognition() {
                    this.isRecognizing = true;
                    this.recognitionResults = [];

                    // 模拟识别过程
                    setTimeout(() => {
                        const mockResults = this.generateMockRecognitionResults();
                        this.recognitionResults = mockResults;
                        this.isRecognizing = false;

                        ElMessage({
                            message: `识别完成，共识别到 ${mockResults.length} 个文件`,
                            type: 'success'
                        });
                    }, 2000);
                },

                // 生成模拟识别结果
                generateMockRecognitionResults() {
                    const possibleFiles = [
                        { name: '现场检查笔录', type: 'document', typeText: '执法文书' },
                        { name: '调查询问笔录', type: 'document', typeText: '执法文书' },
                        { name: '身份证复印件', type: 'certificate', typeText: '身份证明' },
                        { name: '营业执照复印件', type: 'certificate', typeText: '资质证明' },
                        { name: '扣押物品清单', type: 'form', typeText: '清单表格' },
                        { name: '行政处罚决定书', type: 'document', typeText: '执法文书' },
                        { name: '现场照片', type: 'image', typeText: '现场证据' },
                        { name: '违法物品照片', type: 'image', typeText: '物证照片' }
                    ];

                    // 随机选择3-5个文件
                    const selectedCount = Math.floor(Math.random() * 3) + 3;
                    const shuffled = possibleFiles.sort(() => 0.5 - Math.random());
                    const selected = shuffled.slice(0, selectedCount);

                    // 检查是否与已存在文件重复
                    return selected.map(file => ({
                        ...file,
                        isDuplicate: this.existingFiles.includes(file.name),
                        selected: true // 默认全选
                    }));
                },

                // 获取上传按钮文本
                getUploadButtonText() {
                    if (this.isRecognizing) {
                        return '识别中...';
                    }
                    if (this.isUploading) {
                        return '上传中...';
                    }
                    
                    const selectedFiles = this.recognitionResults.filter(item => item.selected);
                    if (selectedFiles.length === 0) {
                        return '请选择文件';
                    }
                    
                    const selectedDuplicateFiles = selectedFiles.filter(item => item.isDuplicate);
                    if (selectedDuplicateFiles.length > 0) {
                        return `上传并覆盖 (${selectedFiles.length}个文件)`;
                    }
                    
                    return `上传 ${selectedFiles.length} 个文件`;
                },

                // 全选/取消全选文件
                selectAllFiles() {
                    const shouldSelectAll = !this.allFilesSelected;
                    this.recognitionResults.forEach(item => {
                        item.selected = shouldSelectAll;
                    });
                    
                    ElMessage({
                        message: shouldSelectAll ? '已全选所有文件' : '已取消全选',
                        type: 'info'
                    });
                },

                // 处理单个项目选择变化
                onItemSelectionChange() {
                    // 检查是否有选中的文件
                    const selectedCount = this.recognitionResults.filter(item => item.selected).length;
                    if (selectedCount === 0) {
                        ElMessage({
                            message: '请至少选择一个文件进行上传',
                            type: 'warning'
                        });
                    }
                },

                // 查看已存在的文件
                viewExistingFile(item) {
                    // 将当前案件信息和文件信息存储到localStorage中，供目标页面使用
                    localStorage.setItem('selectedCase', JSON.stringify(this.currentCase));
                    localStorage.setItem('selectedDocument', JSON.stringify({
                        name: item.name,
                        type: item.type,
                        typeText: item.typeText,
                        isDuplicate: item.isDuplicate
                    }));
                    
                    ElMessage({
                        message: `正在查看文书: ${item.name}`,
                        type: 'info'
                    });
                    
                    // 跳转到案件详情页面
                    window.location.href = 'case-detail.html';
                }
            }
        }).use(ElementPlus).mount('#app');
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    </script>
</body>
</html>