// vite.config.mjs
import {
  defineConfig,
  loadEnv
} from "file:///C:/Users/<USER>/Documents/project/hzyc-frontend/node_modules/.pnpm/vite@5.4.19_sass@1.89.2_terser@5.43.1/node_modules/vite/dist/node/index.js";
import { resolve } from "path";

// vite/plugins/index.js
import vue from "file:///C:/Users/<USER>/Documents/project/hzyc-frontend/node_modules/.pnpm/@vitejs+plugin-vue@5.2.4_vi_42e09cb1521b474d86d8ca09dbbb676b/node_modules/@vitejs/plugin-vue/dist/index.mjs";

// vite/plugins/auto-import.js
import autoImport from "file:///C:/Users/<USER>/Documents/project/hzyc-frontend/node_modules/.pnpm/unplugin-auto-import@0.11.5_ca0f97b3f13a2261d9bae294df542179/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: ["vue", "vue-router", "vuex"],
    dts: false
  });
}

// vite/plugins/compression.js
import compression from "file:///C:/Users/<USER>/Documents/project/hzyc-frontend/node_modules/.pnpm/vite-plugin-compression@0.5_25c6fde02276376cd7ce6dfe3cbba1c5/node_modules/vite-plugin-compression/dist/index.mjs";
function createCompression(env) {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/setup-extend.js
import setupExtend from "file:///C:/Users/<USER>/Documents/project/hzyc-frontend/node_modules/.pnpm/vite-plugin-vue-setup-exten_4cb510b996aaf34667f4aaca96352ad3/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
function createSetupExtend() {
  return setupExtend();
}

// vite/plugins/index.js
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}

// vite.config.mjs
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Documents\\project\\hzyc-frontend";
var vite_config_default = ({
  mode,
  command
}) => {
  const env = loadEnv(mode, process.cwd());
  const {
    VITE_APP_ENV,
    VITE_APP_BASE
  } = env;
  const isProd = VITE_APP_ENV === "production";
  const buildConfig = {
    target: "esnext",
    minify: isProd ? "terser" : "esbuild"
    // 根据环境选择压缩工具
  };
  if (isProd) {
    buildConfig.terserOptions = {
      compress: {
        drop_console: true,
        // 删除 console
        drop_debugger: true
        // 删除 debugger
      },
      format: {
        comments: false
        // 删除所有注释
      }
    };
    buildConfig.rollupOptions = {
      output: {
        manualChunks: {
          "element-plus": ["element-plus"],
          "@smallwei/avue": ["@smallwei/avue"]
        }
      }
    };
  }
  return defineConfig({
    base: VITE_APP_BASE,
    define: {
      __VUE_I18N_FULL_INSTALL__: true,
      __VUE_I18N_LEGACY_API__: true,
      __INTLIFY_PROD_DEVTOOLS__: false
    },
    server: {
      port: 2888,
      proxy: {
        "/api": {
          target: "http://localhost",
          //target: 'https://saber3.bladex.cn/api',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, "")
        }
      }
    },
    resolve: {
      alias: {
        "~": resolve(__vite_injected_original_dirname, "./"),
        "@": resolve(__vite_injected_original_dirname, "./src"),
        components: resolve(__vite_injected_original_dirname, "./src/components"),
        styles: resolve(__vite_injected_original_dirname, "./src/styles"),
        utils: resolve(__vite_injected_original_dirname, "./src/utils")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    plugins: createVitePlugins(env, command === "build"),
    build: buildConfig,
    optimizeDeps: {
      esbuildOptions: {
        target: "esnext"
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
